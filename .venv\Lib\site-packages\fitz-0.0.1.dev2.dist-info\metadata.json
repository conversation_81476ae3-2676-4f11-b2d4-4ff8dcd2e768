{"classifiers": ["Development Status :: 2 - Pre-Alpha", "Intended Audience :: Science/Research", "Programming Language :: Python :: 2.7", "License :: OSI Approved :: BSD License", "Operating System :: POSIX", "Operating System :: Unix", "Operating System :: MacOS"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst"}, "project_urls": {"Home": "http://github.com/kastman/fitz"}}}, "extras": [], "generator": "bdist_wheel (0.29.0)", "keywords": ["neuroimaging", "workflows"], "license": "BSD (3-clause)", "metadata_version": "2.0", "name": "fitz", "run_requires": [{"requires": ["configobj", "configparser", "httplib2", "nibabel", "nipype", "numpy", "pandas", "pyxnat", "scipy"]}], "summary": "Fitz: Workflow Mangement for neuroimaging data.", "version": "0.0.1.dev2"}