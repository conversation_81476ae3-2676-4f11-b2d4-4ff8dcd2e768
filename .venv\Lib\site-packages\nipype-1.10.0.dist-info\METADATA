Metadata-Version: 2.4
Name: nipype
Version: 1.10.0
Summary: Neuroimaging in Python: Pipelines and Interfaces
Home-page: http://nipy.org/nipype
Download-URL: http://github.com/nipy/nipype/archives/master
Author: nipype developers
Author-email: <EMAIL>
Maintainer: nipype developers
Maintainer-email: <EMAIL>
License: Apache License, 2.0
Platform: OS Independent
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX :: Linux
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Scientific/Engineering
Provides: nipype
Requires-Python: >= 3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: click>=6.6.0
Requires-Dist: networkx>=2.5
Requires-Dist: nibabel>=3.0
Requires-Dist: numpy>=1.21
Requires-Dist: packaging
Requires-Dist: prov>=1.5.2
Requires-Dist: pydot>=1.2.3
Requires-Dist: python-dateutil>=2.2
Requires-Dist: rdflib>=5.0.0
Requires-Dist: scipy>=1.8
Requires-Dist: simplejson>=3.8.0
Requires-Dist: traits>=6.2
Requires-Dist: filelock>=3.0.0
Requires-Dist: acres
Requires-Dist: etelemetry>=0.3.1
Requires-Dist: looseversion!=1.2
Requires-Dist: puremagic
Provides-Extra: data
Requires-Dist: datalad; extra == "data"
Provides-Extra: doc
Requires-Dist: dipy; extra == "doc"
Requires-Dist: ipython; extra == "doc"
Requires-Dist: matplotlib; extra == "doc"
Requires-Dist: nbsphinx; extra == "doc"
Requires-Dist: sphinx-argparse; extra == "doc"
Requires-Dist: sphinx>=2.1.2; extra == "doc"
Requires-Dist: sphinxcontrib-apidoc; extra == "doc"
Provides-Extra: duecredit
Requires-Dist: duecredit; extra == "duecredit"
Provides-Extra: maint
Requires-Dist: GitPython; extra == "maint"
Requires-Dist: fuzzywuzzy; extra == "maint"
Provides-Extra: nipy
Requires-Dist: nitime; extra == "nipy"
Requires-Dist: nilearn; extra == "nipy"
Requires-Dist: dipy; extra == "nipy"
Requires-Dist: nipy; extra == "nipy"
Requires-Dist: matplotlib; extra == "nipy"
Provides-Extra: profiler
Requires-Dist: psutil>=5.0; extra == "profiler"
Provides-Extra: pybids
Requires-Dist: pybids>=0.7.0; extra == "pybids"
Provides-Extra: specs
Requires-Dist: black; extra == "specs"
Provides-Extra: ssh
Requires-Dist: paramiko; extra == "ssh"
Provides-Extra: tests
Requires-Dist: coverage>=5.2.1; extra == "tests"
Requires-Dist: pandas>=1.5.0; extra == "tests"
Requires-Dist: pytest>=6; extra == "tests"
Requires-Dist: pytest-cov>=2.11; extra == "tests"
Requires-Dist: pytest-env; extra == "tests"
Requires-Dist: pytest-timeout>=1.4; extra == "tests"
Requires-Dist: pytest-doctestplus; extra == "tests"
Requires-Dist: pytest-xdist>=2.5; extra == "tests"
Requires-Dist: sphinx>=7; extra == "tests"
Provides-Extra: xvfbwrapper
Requires-Dist: xvfbwrapper; extra == "xvfbwrapper"
Provides-Extra: all
Requires-Dist: fuzzywuzzy; extra == "all"
Requires-Dist: pytest-doctestplus; extra == "all"
Requires-Dist: pytest-timeout>=1.4; extra == "all"
Requires-Dist: ipython; extra == "all"
Requires-Dist: pytest-cov>=2.11; extra == "all"
Requires-Dist: pybids>=0.7.0; extra == "all"
Requires-Dist: psutil>=5.0; extra == "all"
Requires-Dist: pytest-env; extra == "all"
Requires-Dist: duecredit; extra == "all"
Requires-Dist: pandas>=1.5.0; extra == "all"
Requires-Dist: black; extra == "all"
Requires-Dist: sphinxcontrib-apidoc; extra == "all"
Requires-Dist: GitPython; extra == "all"
Requires-Dist: sphinx-argparse; extra == "all"
Requires-Dist: coverage>=5.2.1; extra == "all"
Requires-Dist: matplotlib; extra == "all"
Requires-Dist: pytest>=6; extra == "all"
Requires-Dist: nilearn; extra == "all"
Requires-Dist: dipy; extra == "all"
Requires-Dist: nbsphinx; extra == "all"
Requires-Dist: sphinx>=2.1.2; extra == "all"
Requires-Dist: nitime; extra == "all"
Requires-Dist: paramiko; extra == "all"
Requires-Dist: sphinx>=7; extra == "all"
Requires-Dist: xvfbwrapper; extra == "all"
Requires-Dist: pytest-xdist>=2.5; extra == "all"
Requires-Dist: nipy; extra == "all"
Requires-Dist: datalad; extra == "all"
Provides-Extra: dev
Requires-Dist: pytest-env; extra == "dev"
Requires-Dist: matplotlib; extra == "dev"
Requires-Dist: pytest>=6; extra == "dev"
Requires-Dist: pytest-doctestplus; extra == "dev"
Requires-Dist: pytest-timeout>=1.4; extra == "dev"
Requires-Dist: pandas>=1.5.0; extra == "dev"
Requires-Dist: pytest-xdist>=2.5; extra == "dev"
Requires-Dist: black; extra == "dev"
Requires-Dist: dipy; extra == "dev"
Requires-Dist: ipython; extra == "dev"
Requires-Dist: sphinxcontrib-apidoc; extra == "dev"
Requires-Dist: pytest-cov>=2.11; extra == "dev"
Requires-Dist: nbsphinx; extra == "dev"
Requires-Dist: sphinx-argparse; extra == "dev"
Requires-Dist: coverage>=5.2.1; extra == "dev"
Requires-Dist: sphinx>=2.1.2; extra == "dev"
Requires-Dist: sphinx>=7; extra == "dev"
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: download-url
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: platform
Dynamic: provides
Dynamic: provides-extra
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

========================================================
NIPYPE: Neuroimaging in Python: Pipelines and Interfaces
========================================================

Current neuroimaging software offer users an incredible opportunity to
analyze data using a variety of different algorithms. However, this has
resulted in a heterogeneous collection of specialized applications
without transparent interoperability or a uniform operating interface.

*Nipype*, an open-source, community-developed initiative under the
umbrella of `NiPy <http://nipy.org>`_, is a Python project that provides a
uniform interface to existing neuroimaging software and facilitates interaction
between these packages within a single workflow. Nipype provides an environment
that encourages interactive exploration of algorithms from different
packages (e.g., AFNI, ANTS, BRAINS, BrainSuite, Camino, FreeSurfer, FSL, MNE,
MRtrix, MNE, Nipy, Slicer, SPM), eases the design of workflows within and
between packages, and reduces the learning curve necessary to use different packages. Nipype is creating a collaborative platform for neuroimaging software development in a high-level language and addressing limitations of existing pipeline systems.

*Nipype* allows you to:

* easily interact with tools from different software packages
* combine processing steps from different software packages
* develop new workflows faster by reusing common steps from old ones
* process data faster by running it in parallel on many cores/machines
* make your research easily reproducible
* share your processing workflows with the community
