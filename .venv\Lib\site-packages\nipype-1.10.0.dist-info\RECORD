../../Scripts/nipypecli.exe,sha256=BdbM9-7Q9OMXIPH7R20X-pRBZFRdc1QMT8dNoFd1rUg,108390
nipype-1.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
nipype-1.10.0.dist-info/METADATA,sha256=LA7N7LMhjTgra7-nrS-fwFJ-X9Tnzf9yydAh-cqR1xY,7121
nipype-1.10.0.dist-info/RECORD,,
nipype-1.10.0.dist-info/WHEEL,sha256=tTnHoFhvKQHCh4jz3yCn0WPTYIy7wXx3CJtJ7SJGV7c,91
nipype-1.10.0.dist-info/entry_points.txt,sha256=QbJu7Zh0lOjtPQGnF-lYwp4dPaJfg3kZQvpcxMPfSp0,53
nipype-1.10.0.dist-info/licenses/LICENSE,sha256=UXNLBvXvNd31CqWlBfsulm-_Wls43EJf8QxpbZFeA84,633
nipype-1.10.0.dist-info/top_level.txt,sha256=tzp9SEPBonrQ_q2Y38bH3qoqmO9Nm00RQ1-3yFowU1Q,7
nipype/COMMIT_INFO.txt,sha256=TSyCKoQ9mfJDaQxFbvObJhhwUL30KyvCTun9EU45os0,65
nipype/__init__.py,sha256=kTzjrzE9PmK_GNVb5_TAqKE0dBbz5M_xJVWBvOB34eA,2537
nipype/__pycache__/__init__.cpython-311.pyc,,
nipype/__pycache__/conftest.cpython-311.pyc,,
nipype/__pycache__/info.cpython-311.pyc,,
nipype/__pycache__/pkg_info.cpython-311.pyc,,
nipype/__pycache__/refs.cpython-311.pyc,,
nipype/algorithms/__init__.py,sha256=RgDatzF45nhI0HAbxpXZMIoOU6J10mYV0x7bCIdSXJE,237
nipype/algorithms/__pycache__/__init__.cpython-311.pyc,,
nipype/algorithms/__pycache__/confounds.cpython-311.pyc,,
nipype/algorithms/__pycache__/icc.cpython-311.pyc,,
nipype/algorithms/__pycache__/mesh.cpython-311.pyc,,
nipype/algorithms/__pycache__/metrics.cpython-311.pyc,,
nipype/algorithms/__pycache__/misc.cpython-311.pyc,,
nipype/algorithms/__pycache__/modelgen.cpython-311.pyc,,
nipype/algorithms/__pycache__/rapidart.cpython-311.pyc,,
nipype/algorithms/__pycache__/stats.cpython-311.pyc,,
nipype/algorithms/confounds.py,sha256=BwdmR8-tOYjgHR4_Kl3ZZM6soa2dbpzlUXeeDN5JIbg,53471
nipype/algorithms/icc.py,sha256=o_qYcLOjw27cii-G1l_so31Cqy8PDXk8bw_8osd4qxk,5489
nipype/algorithms/mesh.py,sha256=En9VBZtyh0rUNAjuV9wYgQenC4Cz_mBNKvesdMrDbyM,13142
nipype/algorithms/metrics.py,sha256=o6wonJd5G0nPhYTGS9EB6qMdse9B6gYxjXbPYIQZyDU,25725
nipype/algorithms/misc.py,sha256=tTT58BhcfpVelVxkkSNqOoDMAmbb7pAphfPup4SswUI,52328
nipype/algorithms/modelgen.py,sha256=S59kU8kFfjt5S1oxAVkK9xpjG5AfhNAGHrpU0evm2iU,38918
nipype/algorithms/rapidart.py,sha256=_dk_6U30V1rWNNz_Jr0aGU1uBrAS-IGPjZNkMBW6DqA,29665
nipype/algorithms/stats.py,sha256=fBubBCyuVxlXSvmaUnQy7EO4M58FcjlqeIzseSJRP2s,2436
nipype/algorithms/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/algorithms/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_CompCor.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_ErrorMap.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_Overlap.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_TSNR.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ACompCor.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ActivationCount.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_AddCSVColumn.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_AddCSVRow.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_AddNoise.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ArtifactDetect.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_CalculateMedian.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_CalculateNormalizedMoments.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ComputeDVARS.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ComputeMeshWarp.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_CreateNifti.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_Distance.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_FramewiseDisplacement.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_FuzzyOverlap.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_Gunzip.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_Gzip.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ICC.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_Matlab2CSV.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_MergeCSVFiles.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_MergeROIs.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_MeshWarpMaths.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_ModifyAffine.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_NonSteadyStateDetector.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_NormalizeProbabilityMapSet.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_P2PDistance.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_PickAtlas.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_Similarity.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_SimpleThreshold.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_SpecifyModel.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_SpecifySPMModel.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_SpecifySparseModel.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_SplitROIs.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_StimulusCorrelation.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_TCompCor.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_TVTKBaseInterface.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_auto_WarpPoints.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_confounds.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_icc_anova.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_mesh_ops.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_metrics.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_misc.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_modelgen.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_moments.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_normalize_tpms.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_rapidart.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_splitmerge.cpython-311.pyc,,
nipype/algorithms/tests/__pycache__/test_stats.cpython-311.pyc,,
nipype/algorithms/tests/test_CompCor.py,sha256=fwCoAf_r6o72xbydOMNNZfSIukLaAIg4xS5--YnIDDw,10412
nipype/algorithms/tests/test_ErrorMap.py,sha256=cTTE8-wMSlZX3LLr6tlCHK_Y2JN8I2w0L2hb8AJxUqQ,2466
nipype/algorithms/tests/test_Overlap.py,sha256=mwlOyJq35RztWZQtyA6oUGV181muaK1I-Vk3VUNkaVU,1119
nipype/algorithms/tests/test_TSNR.py,sha256=OS0n2187b88OekprX2F5OhW1Y2YeSAHWFFWeoM7mtMQ,4247
nipype/algorithms/tests/test_auto_ACompCor.py,sha256=z7F-FxNJrkiSFDRQuBLp0NnA090nHUGfL-6I6ea-rxA,2090
nipype/algorithms/tests/test_auto_ActivationCount.py,sha256=mv3Vjv0tphVEIpT9htSzlAMghm6vPiT-98yscBQQ5H4,966
nipype/algorithms/tests/test_auto_AddCSVColumn.py,sha256=zBUK7jx6gDN_OjMhdiqD3hk2hZteyVwErXMQPyK3_uY,948
nipype/algorithms/tests/test_auto_AddCSVRow.py,sha256=loNsSZj2jNdgPHS0VqOpYd_VkMfO9ma8FxmJbc6NDQ8,839
nipype/algorithms/tests/test_auto_AddNoise.py,sha256=nMV47ughNz3AeN00BTrjyh0RbEN2on6UeWfa4nYMXy4,1131
nipype/algorithms/tests/test_auto_ArtifactDetect.py,sha256=54zyfFTPxVmDTAH1nd96jWm2s9CoiiRnBVZ7Ax7GjtQ,2176
nipype/algorithms/tests/test_auto_CalculateMedian.py,sha256=Ncu_p-CflsvrekzBjvaFZ1oSynHk9SkXFdoD9GeCeFc,805
nipype/algorithms/tests/test_auto_CalculateNormalizedMoments.py,sha256=nPwu2DnN_T9Rovae6IslH0KwF9leaGhCng2CIKmHLcU,890
nipype/algorithms/tests/test_auto_ComputeDVARS.py,sha256=tv-v2lq4VcGeo6gU92oDkyqrZA0yOaPj-Ma__06_pz4,2089
nipype/algorithms/tests/test_auto_ComputeMeshWarp.py,sha256=hLf3EppPlW6pFFPspNUshpZMVQxs4Z9ekr54ZdsAxJU,1295
nipype/algorithms/tests/test_auto_CreateNifti.py,sha256=dkE4HmcMzs21qgt2ijtGDMaCkMK4OVGRl5g6iyvEL8Y,907
nipype/algorithms/tests/test_auto_Distance.py,sha256=RXeQFuyKSnKOTUddPKw-pZ23XQuIRNNXHF1w5gruzQY,1060
nipype/algorithms/tests/test_auto_FramewiseDisplacement.py,sha256=N4nJYj6FANqDlbVv3mdWqCVv7cqehd-MuGAY9YqOx1s,1527
nipype/algorithms/tests/test_auto_FuzzyOverlap.py,sha256=fIdEHaUf74eIKULKxlh0wdSMAUzOypHHj_S7JdroIOg,1073
nipype/algorithms/tests/test_auto_Gunzip.py,sha256=_g_Sd6U1gyBOKPDKLQBenYgLPpOxvLoMK_Nk_tfRB9M,820
nipype/algorithms/tests/test_auto_Gzip.py,sha256=VB9DwJxJAinXxvQy0oWJbLtH67tEV49pdEvzHVGkc8M,810
nipype/algorithms/tests/test_auto_ICC.py,sha256=3H_twjfZgEka3AEVfU9SRes08wBs-k2dUuHtEWQT6SM,952
nipype/algorithms/tests/test_auto_Matlab2CSV.py,sha256=sGJYV40oRJME8yTMPgPbuCIiAEdQAg3OYrBqzkRWx8s,813
nipype/algorithms/tests/test_auto_MergeCSVFiles.py,sha256=SvItkImAXHUJxG2UpR9_S6zafIVQ2FAZKWzdNkYnS5c,1058
nipype/algorithms/tests/test_auto_MergeROIs.py,sha256=1M6kkOjKapUIuQktzE3upGTpHiWtpKeFP5U6EXNE12I,806
nipype/algorithms/tests/test_auto_MeshWarpMaths.py,sha256=V5VuRA9sPxpUCZxzYECy3rnxe5ga0oEuE9fekJbN094,1226
nipype/algorithms/tests/test_auto_ModifyAffine.py,sha256=EKTZgmvcDMX2AKsIuKqzEn-VFrqiSSHKrn-VTBUKlDI,811
nipype/algorithms/tests/test_auto_NonSteadyStateDetector.py,sha256=WAirxjSFi47KJuM4BhsTcTDRBUhCVsJBan6FTDf_Fng,820
nipype/algorithms/tests/test_auto_NormalizeProbabilityMapSet.py,sha256=4kP4FkAFUROsUecmLAxVIVE-lk9VbB_kutRU6cjpFRk,821
nipype/algorithms/tests/test_auto_P2PDistance.py,sha256=qnDLbb054NHSowjG3O-IIGnLKxtGvITPsmGpKzjHiWI,1275
nipype/algorithms/tests/test_auto_PickAtlas.py,sha256=uZ4VRACZm6ZbNCuWP8k8dIim0doG8EE7KqgmU4yY48Y,1028
nipype/algorithms/tests/test_auto_Similarity.py,sha256=fnNIz5IfasdYxw2AVTPxzjiMB65QB8amh-WyIxMCJ6Y,1019
nipype/algorithms/tests/test_auto_SimpleThreshold.py,sha256=7WXgXKyyxexmaHYTvzdEq56XJX35XIt5UU1N4rsS9PM,813
nipype/algorithms/tests/test_auto_SpecifyModel.py,sha256=8Qe8EN0EkGjq5cZWJZZV6X-IDrAmBuW5z8Ad3x5Tt2c,1711
nipype/algorithms/tests/test_auto_SpecifySPMModel.py,sha256=CQMXXvj-IBfagJ6F5BE5N5D7qB_g4suSieeIK91grNw,1864
nipype/algorithms/tests/test_auto_SpecifySparseModel.py,sha256=yPTp9tumXD_kpFkuKfYTMoiEPTILzlsDLFWuXbrM47Q,2366
nipype/algorithms/tests/test_auto_SplitROIs.py,sha256=aXMiw7JkLaX78iQXwXhgU5TmE4tPopkOIV8XUmymhxw,878
nipype/algorithms/tests/test_auto_StimulusCorrelation.py,sha256=-2BWgpEFQ73-0-QpL6l3isn4-E6bZfYeqXRpuzO6EP0,1022
nipype/algorithms/tests/test_auto_TCompCor.py,sha256=bc1xJLlpP6HDUj6der_ScW93FyPLrYgqU4IhTWO74tU,2201
nipype/algorithms/tests/test_auto_TVTKBaseInterface.py,sha256=HUr47GEsGYba7PmjtRz6BkXBnqyhrL1QseRiWCQ6ays,369
nipype/algorithms/tests/test_auto_WarpPoints.py,sha256=DywGtjl3k8QKb_TwVbXfYWv-46XvKoaSvM6FEmAA4sw,1167
nipype/algorithms/tests/test_confounds.py,sha256=t133jH7pSrNdScBZ7DOIavG7duURg7nvS8uv_fYkNd4,2257
nipype/algorithms/tests/test_icc_anova.py,sha256=pxLtfBsrAtLHaIGkmxaCzhoPKCh3tCJMEEx_cNPK464,671
nipype/algorithms/tests/test_mesh_ops.py,sha256=uveWwNBzjcGPvio4MmBf7_qgwW473g8QqqMSNhaYUX0,2400
nipype/algorithms/tests/test_metrics.py,sha256=YwmWVRqBs9VY0yNR1Pg71Xw5fh2s1t_StwD6v29XHgQ,1921
nipype/algorithms/tests/test_misc.py,sha256=-ptWH8LMUT_cf8YNhRKhkdG8gRuPgBP71esD7g-Zxfc,1309
nipype/algorithms/tests/test_modelgen.py,sha256=dWt20NNFRX5hfLlaiAq8fRraGVOpZZH29ZIq2zmNWyk,9644
nipype/algorithms/tests/test_moments.py,sha256=zfNPvQv99sPzXoZndrk_A6-piCDdEUfVoY0JQY2tYGI,13485
nipype/algorithms/tests/test_normalize_tpms.py,sha256=0oD7ovhzYfECDb88RlgROKzFSDwpUuCJ1CEeMRQRvJE,1299
nipype/algorithms/tests/test_rapidart.py,sha256=zbh9gH_mgVY0_HyIubd8rltYQHNeyAyJAEmM-qA8cw8,3421
nipype/algorithms/tests/test_splitmerge.py,sha256=wmQTPqQBtU9mdQc9IEWfm5UuLuaA7nmQkX3xzEZOk7I,903
nipype/algorithms/tests/test_stats.py,sha256=iKPdU3c3wO_52HUQfO9aTbHpEtNTMxswZqsmkCdErzM,1640
nipype/caching/__init__.py,sha256=WRq8Azec8h8yI3CGEPJnjVUMyyle2iaNn4eYNJtOewQ,27
nipype/caching/__pycache__/__init__.cpython-311.pyc,,
nipype/caching/__pycache__/memory.cpython-311.pyc,,
nipype/caching/memory.py,sha256=vx-kjXEnG6cIgqlSuBavh-oUuKUo8xQ_IUiDwZj_Oys,10280
nipype/caching/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/caching/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/caching/tests/__pycache__/test_memory.cpython-311.pyc,,
nipype/caching/tests/test_memory.py,sha256=P4-3l8grIWt0q1J6CankRVMxBqspvPgbmCp7yZbCD3w,1346
nipype/conftest.py,sha256=J9RWSuuLrOvechX0-pLyz5q1b-hdSMlATrsRTGTuSqs,1333
nipype/external/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/external/__pycache__/__init__.cpython-311.pyc,,
nipype/external/__pycache__/cloghandler.cpython-311.pyc,,
nipype/external/__pycache__/due.cpython-311.pyc,,
nipype/external/__pycache__/fsl_imglob.cpython-311.pyc,,
nipype/external/cloghandler.py,sha256=J99yY6FGLLWMLLOaRDUCotGpaWz5pbvn4Qa98-Hsrr0,14526
nipype/external/d3.js,sha256=VXMTbUBnoQt0f0Gnk63wY_E7lC-yEUPcFATThzKLI3U,326262
nipype/external/due.py,sha256=-AQpp6xPNUoMR_XS_332wUMRgwgfXhfdCcApcp-HVv8,1773
nipype/external/fsl_imglob.py,sha256=CrQMjhxPwvzq3x1b9mz3cldl7q7MVf5wkpBTnq7BSt0,5577
nipype/info.py,sha256=gv0J8NtPGuyL8kcL5LziZMIAHYVEPFtTVohegjH86_Q,6519
nipype/interfaces/__init__.py,sha256=liuGOxhNC_kl6z2HjImlkgMWVLq49pgo3VudhOttkm8,412
nipype/interfaces/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/__pycache__/bru2nii.cpython-311.pyc,,
nipype/interfaces/__pycache__/c3.cpython-311.pyc,,
nipype/interfaces/__pycache__/dcm2nii.cpython-311.pyc,,
nipype/interfaces/__pycache__/dcmstack.cpython-311.pyc,,
nipype/interfaces/__pycache__/dynamic_slicer.cpython-311.pyc,,
nipype/interfaces/__pycache__/image.cpython-311.pyc,,
nipype/interfaces/__pycache__/io.cpython-311.pyc,,
nipype/interfaces/__pycache__/matlab.cpython-311.pyc,,
nipype/interfaces/__pycache__/meshfix.cpython-311.pyc,,
nipype/interfaces/__pycache__/nilearn.cpython-311.pyc,,
nipype/interfaces/__pycache__/petpvc.cpython-311.pyc,,
nipype/interfaces/__pycache__/quickshear.cpython-311.pyc,,
nipype/interfaces/__pycache__/r.cpython-311.pyc,,
nipype/interfaces/__pycache__/vtkbase.cpython-311.pyc,,
nipype/interfaces/afni/__init__.py,sha256=YP-jAs6Vgz_Zc_VIdX62FwUYfO7yiRZAGMkurJOpTGc,1426
nipype/interfaces/afni/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/afni/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/afni/__pycache__/model.cpython-311.pyc,,
nipype/interfaces/afni/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/afni/__pycache__/svm.cpython-311.pyc,,
nipype/interfaces/afni/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/afni/base.py,sha256=ESrwu2CMushU_E9viRqvQZvYhe4EIK0b7ic2zQASngg,9916
nipype/interfaces/afni/model.py,sha256=yT4W-mNTwhO5149bZk8Oo8VU3sSMdh-nELK_iGO9Yqc,27661
nipype/interfaces/afni/preprocess.py,sha256=VTkIy7xl188pskty_7q2Zet_EU5oUu2oQO1A_n37nUk,155204
nipype/interfaces/afni/svm.py,sha256=1zjKLKWXk5o35fKc9vy1Rh9VJjQEEMYKQQLYIqEFOfs,5747
nipype/interfaces/afni/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/interfaces/afni/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ABoverlap.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AFNICommand.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AFNICommandBase.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AFNIPythonCommand.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AFNItoNIFTI.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AlignEpiAnatPy.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Allineate.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AutoTLRC.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_AutoTcorrelate.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Autobox.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Automask.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Axialize.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Bandpass.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_BlurInMask.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_BlurToFWHM.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_BrickStat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Bucket.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Calc.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Cat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_CatMatvec.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_CenterMass.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ClipLevel.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ConvertDset.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Copy.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Deconvolve.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_DegreeCentrality.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Despike.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Detrend.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Dot.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ECM.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Edge3.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Eval.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_FWHMx.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Fim.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Fourier.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_GCOR.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Hist.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_LFCD.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_LocalBistat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Localstat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_MaskTool.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Maskave.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Means.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Merge.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_NetCorr.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Notes.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_NwarpAdjust.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_NwarpApply.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_NwarpCat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_OneDToolPy.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_OutlierCount.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_QualityIndex.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Qwarp.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_QwarpPlusMinus.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ROIStats.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ReHo.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Refit.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Remlfit.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Resample.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Retroicor.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_SVMTest.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_SVMTrain.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Seg.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_SkullStrip.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Synthesize.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TCat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TCatSubBrick.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TCorr1D.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TCorrMap.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TCorrelate.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TNorm.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TProject.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TShift.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TSmooth.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_TStat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_To3D.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Undump.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Unifize.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Volreg.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Warp.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_ZCutUp.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Zcat.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_auto_Zeropad.cpython-311.pyc,,
nipype/interfaces/afni/tests/__pycache__/test_extra_Deconvolve.cpython-311.pyc,,
nipype/interfaces/afni/tests/test_auto_ABoverlap.py,sha256=J0aKZgl5LDn5NTrMhMAD5S4VHHqPbUtRuq1f9JOt2HQ,1598
nipype/interfaces/afni/tests/test_auto_AFNICommand.py,sha256=VvwwQt4xoTAUn-YL249rWAll55TEvsuF4-LXG55sWZ0,786
nipype/interfaces/afni/tests/test_auto_AFNICommandBase.py,sha256=lcj4NFg3QWy6iOVbXXSbE1Ym31_28sIR4OkgUUueBHI,510
nipype/interfaces/afni/tests/test_auto_AFNIPythonCommand.py,sha256=3_yEpTLJ_fPmHtxV3gfH8M_eVaeKADnobFaEpyaiOgo,804
nipype/interfaces/afni/tests/test_auto_AFNItoNIFTI.py,sha256=tP22AXPAyTABt39W-j-YTh7yZVlan3AtIsFPvEIHDi4,1616
nipype/interfaces/afni/tests/test_auto_AlignEpiAnatPy.py,sha256=fOLtEbzfn-A9IaTsS_x60tX6QhnW7V_-PPfYmpCafI8,2513
nipype/interfaces/afni/tests/test_auto_Allineate.py,sha256=e8L7ICNre8g2yxwfRANTMevLYmgNAF9F4AmDpRrNKiA,5569
nipype/interfaces/afni/tests/test_auto_AutoTLRC.py,sha256=yQbABgo5mMASyeStcLf7W84EQtkZxqPzM4I-Exqf_1k,1155
nipype/interfaces/afni/tests/test_auto_AutoTcorrelate.py,sha256=OOIAUW2GSMGPcwQl-zDs5nEJrkVT5Z9Ta8YSNGADPnw,1802
nipype/interfaces/afni/tests/test_auto_Autobox.py,sha256=fZMu1Wj90H8s7kGzxd7r44j458pBD28uISzV9Uzit58,1524
nipype/interfaces/afni/tests/test_auto_Automask.py,sha256=9oZzice-myhgKZU1ku13ZwhO_6Dpkn_QW3vrh2ZisQ4,1719
nipype/interfaces/afni/tests/test_auto_Axialize.py,sha256=M_LGf8Mx7xJH3EnUaGoyyZFfADwdx0XzfcZ4c9ZsHkk,1718
nipype/interfaces/afni/tests/test_auto_Bandpass.py,sha256=aLVufRuss60nJkQjfdSOZ5JvmRZfb9NJ3WqIcnHC-K0,2393
nipype/interfaces/afni/tests/test_auto_BlurInMask.py,sha256=3Nd4Em4GWX2km0V3nIFQGr693C0djcb1oY4K-XQej2o,1877
nipype/interfaces/afni/tests/test_auto_BlurToFWHM.py,sha256=-ilgUtxozrw9sb-FFJrgCR5xU-m4ULemvjhF4E_Uudw,1633
nipype/interfaces/afni/tests/test_auto_BrickStat.py,sha256=c1Zn7azBj86NSHCzfW2dacroo6hjJ8McUXpwsj03OWI,1493
nipype/interfaces/afni/tests/test_auto_Bucket.py,sha256=m3T3Or-PUIJsmhKKCh0iSCEC5kNhQFBYLwCeQeP6NPc,1173
nipype/interfaces/afni/tests/test_auto_Calc.py,sha256=qOANxuQ0-O0cHMHvr2BbtW3yGw5mW17qswiKVoiQjV4,1901
nipype/interfaces/afni/tests/test_auto_Cat.py,sha256=p1lhyRD400zUIhmrgL9BfhaIOYRS-DUqU8xdRMbOdRs,2274
nipype/interfaces/afni/tests/test_auto_CatMatvec.py,sha256=TiQeuSzgccivF-B_MKsmgK4oY_BXo0l2RXlhHHL5ARY,1626
nipype/interfaces/afni/tests/test_auto_CenterMass.py,sha256=cQRyMvuaW_1HJnm2noqyHSDm1e9N_YdTbv93N3403JI,1766
nipype/interfaces/afni/tests/test_auto_ClipLevel.py,sha256=HDUaOiYZfcJ0Fjxz3-NpxDTdMz2P414Daqf9MnggJ1I,1270
nipype/interfaces/afni/tests/test_auto_ConvertDset.py,sha256=eZtMgmLOV5ZoiPHqVJF3bcYavEH7TjxSvqYaLkFLvps,1367
nipype/interfaces/afni/tests/test_auto_Copy.py,sha256=mn4xa8oFUvegrYKuymCOQWfBeqT-nJvQZjTSL1wxcjY,1336
nipype/interfaces/afni/tests/test_auto_Deconvolve.py,sha256=j8dAeZ3aCypSR5SxYplew02wHIh0n701jvW-g9795ik,4603
nipype/interfaces/afni/tests/test_auto_DegreeCentrality.py,sha256=X2mtrHgbo0ga_aXewoCblsCvwcmSJwfhe2445Z2EBNk,1877
nipype/interfaces/afni/tests/test_auto_Despike.py,sha256=UrNiV0-QIivh5dSJkswZpvVcO8xn0UUUHFXYLrn3WQ0,1281
nipype/interfaces/afni/tests/test_auto_Detrend.py,sha256=vqbniM-R_CpmSOHatc64y_ivNRN4kLvAukCVaNkqxaQ,1281
nipype/interfaces/afni/tests/test_auto_Dot.py,sha256=AZdwuyR39Zet-1Dg0ZLEyUGzzTdZm9lSRzWst5xdQiU,1904
nipype/interfaces/afni/tests/test_auto_ECM.py,sha256=wRZg8D8YrAQKk9PlMjFrZbOD1o27CJslFJLT6gdpqkk,2116
nipype/interfaces/afni/tests/test_auto_Edge3.py,sha256=OfL7kIbcCISiBvZ3RWIR74Bkj8u7zJTb6OHfkqeBLOQ,1822
nipype/interfaces/afni/tests/test_auto_Eval.py,sha256=X3kuYqjrJqWfeWbgrlO09wz0zl5BvE-dqFqelJg5_C4,1890
nipype/interfaces/afni/tests/test_auto_FWHMx.py,sha256=aVgpKvaCSpoQhVKGsRi7WrlGRpCdLzpRLhvxU4IXdhs,2658
nipype/interfaces/afni/tests/test_auto_Fim.py,sha256=KWltpt9zLaB1frBUy9wcW9g9EVrYZCU7UDNhENeTP20,1591
nipype/interfaces/afni/tests/test_auto_Fourier.py,sha256=caJQmdAkSwa5rZMdHwFxZ2Mru5nx0vly2xwWFId0edw,1537
nipype/interfaces/afni/tests/test_auto_GCOR.py,sha256=oh3uqYdyTpjHua-PI4lD0cgLNIssENbiFRul1DYwW_s,1187
nipype/interfaces/afni/tests/test_auto_Hist.py,sha256=0KD_h5tUr6qWR8Yc-Ws0fJQuTxbLc7mBjpy514DlcMU,1921
nipype/interfaces/afni/tests/test_auto_LFCD.py,sha256=tkP25i4BHYFUD1-YQ-pbKAKAZm4ciPRuNVWbhm4hloM,1617
nipype/interfaces/afni/tests/test_auto_LocalBistat.py,sha256=Ou9bUnWHs9xd6Y2A6B_6xnnt-xIYMnTjeoMj8MHd5OM,1985
nipype/interfaces/afni/tests/test_auto_Localstat.py,sha256=v26pnUVHGpA6jqUuwNZsZpmSXx2n9p4IEGg4Mefhihw,2401
nipype/interfaces/afni/tests/test_auto_MaskTool.py,sha256=OMMGGO_TVTjTxgkfVlrzkDjg-t3_54VnxkhJ0OZZ6Y4,1986
nipype/interfaces/afni/tests/test_auto_Maskave.py,sha256=X9m3DH7N_doetQtNCTdHdRL4lKk0kDp6XGMG3HgDJZc,1534
nipype/interfaces/afni/tests/test_auto_Means.py,sha256=F4cRLyvjo2buRlhs2OEmN_uRBVvmoZiGgYI6mBNuSj4,1924
nipype/interfaces/afni/tests/test_auto_Merge.py,sha256=N5FKFoTpkMXZ21zsKoG9BDqgKAPQHFtZpZKrlGuEay0,1392
nipype/interfaces/afni/tests/test_auto_NetCorr.py,sha256=7vxt4VfLcxnLfxtBzI7A-0Ck4kMr5cRFQg-z9YM-2JE,2452
nipype/interfaces/afni/tests/test_auto_Notes.py,sha256=dCVOaDrNOn_fE5GIplmthDCdIlUiQBDaLQEvD3LhwQE,1559
nipype/interfaces/afni/tests/test_auto_NwarpAdjust.py,sha256=SZpTaFwxP-e1i3xalsInToMB0ngdVkmK38kMs5Q2QM8,1359
nipype/interfaces/afni/tests/test_auto_NwarpApply.py,sha256=eyGh2qSNQTxNTEaususMx1slm-XGG-EHHWIwz-ibm7Q,1736
nipype/interfaces/afni/tests/test_auto_NwarpCat.py,sha256=w-VzGkdtD1ltaWPiKlyRegGMlZzVBbwJ2vjmlqoT6bQ,1568
nipype/interfaces/afni/tests/test_auto_OneDToolPy.py,sha256=MJ526HblOWZch321SIuCfKbcQ4gY2F_wXZrClQtDv-w,2076
nipype/interfaces/afni/tests/test_auto_OutlierCount.py,sha256=ItOW4GLiNJzn-x5He_j3NfLVdnHBeUYDhAl7PNBE3aE,2362
nipype/interfaces/afni/tests/test_auto_QualityIndex.py,sha256=R_TI40M2pdcxC0imyYFO2f3FXPv3scoGkW2BK7t3hUE,1930
nipype/interfaces/afni/tests/test_auto_Qwarp.py,sha256=QX1eR2OK_6asx_N_Si06p70VwkLzjf6PvNzNAH0JdX0,5685
nipype/interfaces/afni/tests/test_auto_QwarpPlusMinus.py,sha256=1Ena3S-7CgQGH02ERLTcKWXXiTuXRLhrgYA02zFFA6A,5954
nipype/interfaces/afni/tests/test_auto_ROIStats.py,sha256=anz3vFHI6XHxK_fp0OoOW1y5JAfpaQG6PyzTYKipG5o,2317
nipype/interfaces/afni/tests/test_auto_ReHo.py,sha256=lVdWCDfBC4UfF2JCT2CuV_FcLovTcvkBS2RrNmoZYYQ,1925
nipype/interfaces/afni/tests/test_auto_Refit.py,sha256=s2hlqcFg8m9LOVGPZn5pbYkHSh6b_elvHEZhnARymAM,2081
nipype/interfaces/afni/tests/test_auto_Remlfit.py,sha256=fD8dAwKMKJhnWDjd87qTgURlER5KYJCgZNM3pVe77w0,4874
nipype/interfaces/afni/tests/test_auto_Resample.py,sha256=YM3U4jzAp32DwFs0SFfiSwdn6uxTzcwUg1K71rJZrM8,1597
nipype/interfaces/afni/tests/test_auto_Retroicor.py,sha256=rkMRhLLP0YvX7HjncfPjGWHAlgZf2p-9DlbbXViHWZs,2043
nipype/interfaces/afni/tests/test_auto_SVMTest.py,sha256=OFYxaScOldPob3T6oWIf2A4rGC3Y2x3zUSoTL-ctCgE,1738
nipype/interfaces/afni/tests/test_auto_SVMTrain.py,sha256=sf_a_KgIJcfJP5FRTAFjgb_TwGPp9e7PnT6hD1GYC80,2572
nipype/interfaces/afni/tests/test_auto_Seg.py,sha256=HLqiqECGiAIaFjEV_0Bu4W50O8iPybMVsqJGSl4HJd4,1704
nipype/interfaces/afni/tests/test_auto_SkullStrip.py,sha256=8bA7oHtI6GQt3S1PBkmtOAedYQIJqsiBgvlXNKrL8WM,1305
nipype/interfaces/afni/tests/test_auto_Synthesize.py,sha256=Kfo8MIT5H3pv9FrAOawn4Yb4k9KT4LJyQx8XDMufVn4,1660
nipype/interfaces/afni/tests/test_auto_TCat.py,sha256=ic6n-GAu3DVNDBoz-G-phm2KAFlkK7GR15nk_E4glIE,1375
nipype/interfaces/afni/tests/test_auto_TCatSubBrick.py,sha256=u1-5-TCJLyUrZ26z52jAUv344eKblCP6zP9Jqd9YzUo,1312
nipype/interfaces/afni/tests/test_auto_TCorr1D.py,sha256=5M4FMOP7HPDX9c5LoqFZmSKyk-md4VLZgXQHZappIpI,2018
nipype/interfaces/afni/tests/test_auto_TCorrMap.py,sha256=UB8rYS-pTF4l-rIuCOuXoRx6w2WQTiswvvgqrDiq8gY,5717
nipype/interfaces/afni/tests/test_auto_TCorrelate.py,sha256=f5kb6Lm8dcBdrZ0ouVXgmD3bvtk_qpE1oIBsVNwgOwY,1582
nipype/interfaces/afni/tests/test_auto_TNorm.py,sha256=l6J7SNfnRZtJO4ZCBlQnF0jUUW1MnoqvJh6_PvTHmEI,1634
nipype/interfaces/afni/tests/test_auto_TProject.py,sha256=pjL77kJXHegMrPSSwgrbjpDYTszXyUZkEqUES8XBAIA,2427
nipype/interfaces/afni/tests/test_auto_TShift.py,sha256=IKQxyiD72dTweimkIWGWzzpUC5Aozx3_5yWF7yOEqns,2106
nipype/interfaces/afni/tests/test_auto_TSmooth.py,sha256=IYId3fmw8GFQQsi0jdzOCEiOJPacVSKZqrZ6O3rMyf4,1871
nipype/interfaces/afni/tests/test_auto_TStat.py,sha256=6VD8EKX6IXktlK3u6NKlEkU9P9T-PErAK5y3nPDwuSg,1412
nipype/interfaces/afni/tests/test_auto_To3D.py,sha256=jEEQ-ZnOSViO5_AUX5aHfD_VC38vV1oFIN1eQtl1CLE,1567
nipype/interfaces/afni/tests/test_auto_Undump.py,sha256=mE4-W9EKgpSpgsLt4JLguHCJuvgHRP53Ti8Jn-f3Qro,1808
nipype/interfaces/afni/tests/test_auto_Unifize.py,sha256=_DtfznoVu2W3Vy5fANXsHuBGL4kumgRUk8J6blVUyjA,2054
nipype/interfaces/afni/tests/test_auto_Volreg.py,sha256=YS4k27B-AIliQyzbBg58JU2jmcQ_C6jR6clrIdro6mk,2666
nipype/interfaces/afni/tests/test_auto_Warp.py,sha256=MTzgmSYQxmRQSbbSdy1Ezk8-1itcbnFr4cgE4AkLT6c,2178
nipype/interfaces/afni/tests/test_auto_ZCutUp.py,sha256=5gNdAkdK7T6u8cnn8ZcftEe4te9562br0IhMTpwLcC8,1331
nipype/interfaces/afni/tests/test_auto_Zcat.py,sha256=4eKEPC857ZgzwM46zZrCSBGv0Y4k2urs3CMdPJFdXfY,1532
nipype/interfaces/afni/tests/test_auto_Zeropad.py,sha256=qqDd2_v1rZEPz0hj-8U_vBKq67mLjzSCm_cfVKQDjg0,2328
nipype/interfaces/afni/tests/test_extra_Deconvolve.py,sha256=n510nj3NyBJkLUcl-VuELyo_DiOuauf5ucLby7llBOQ,332
nipype/interfaces/afni/utils.py,sha256=YRHG72RM-cxcg0YSLcr9wb7vTPdljGgR7EvaTgRg8LU,117134
nipype/interfaces/ants/__init__.py,sha256=BUH9l15ZubGg1gCff2dmsrORagn159hTX5fJUDbvTew,1847
nipype/interfaces/ants/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/legacy.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/resampling.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/segmentation.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/ants/__pycache__/visualization.cpython-311.pyc,,
nipype/interfaces/ants/base.py,sha256=UMgCdb0eDWlvI43FehsA8eP28znOBxOK0xx0is9yYbU,4694
nipype/interfaces/ants/legacy.py,sha256=tb09HzwE130Ux30Du5Xzm46jbg6EHTeansz94y2edF4,11701
nipype/interfaces/ants/registration.py,sha256=BCzT6GGf8MsaplgcNDv5fAWPBGvX3AUhGx4U1HQOC78,81352
nipype/interfaces/ants/resampling.py,sha256=2gjGpVnjwnzBoIickp8jNVB3D9nTyOo1bPzh_qG28QI,23063
nipype/interfaces/ants/segmentation.py,sha256=JZhIpidETiqH44N8wDuGWLjOsCHjWUVUwDgMg6M7dX8,69045
nipype/interfaces/ants/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/interfaces/ants/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_AI.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ANTS.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ANTSCommand.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_AffineInitializer.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ApplyTransforms.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ApplyTransformsToPoints.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_Atropos.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_AverageAffineTransform.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_AverageImages.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_BrainExtraction.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ComposeMultiTransform.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_CompositeTransformUtil.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ConvertScalarImageToRGB.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_CorticalThickness.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_CreateJacobianDeterminantImage.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_CreateTiledMosaic.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_DenoiseImage.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_GenWarpFields.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ImageMath.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_JointFusion.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_KellyKapowski.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_LabelGeometry.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_LaplacianThickness.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_MeasureImageSimilarity.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_MultiplyImages.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_N4BiasFieldCorrection.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_Registration.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_RegistrationSynQuick.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ResampleImageBySpacing.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_ThresholdImage.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_WarpImageMultiTransform.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_WarpTimeSeriesImageMultiTransform.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_antsIntroduction.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_auto_buildtemplateparallel.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_extra_Registration.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_resampling.cpython-311.pyc,,
nipype/interfaces/ants/tests/__pycache__/test_segmentation.cpython-311.pyc,,
nipype/interfaces/ants/tests/test_auto_AI.py,sha256=kuRJ5rsSnRAygGDgfG3zBzs_BvADFRmGyp9Ew349UBE,2211
nipype/interfaces/ants/tests/test_auto_ANTS.py,sha256=A3MwrH1u-yHGvgVaI9pHm8pWprEkGxuHAe44OY6MBis,3324
nipype/interfaces/ants/tests/test_auto_ANTSCommand.py,sha256=818wMvC3Rh0yf5zmWifs5gKDAyCoM9YNqq35Nlw2aP0,589
nipype/interfaces/ants/tests/test_auto_AffineInitializer.py,sha256=Ei52TwPBmxFPviVfgVF8yuSTOi5QgRo6xEv1JlpPXC8,1971
nipype/interfaces/ants/tests/test_auto_ApplyTransforms.py,sha256=3XRgG8xoIeRQLVjQN9ZFHyrkJ_5UK6Na2X1G9BlFhwc,2142
nipype/interfaces/ants/tests/test_auto_ApplyTransformsToPoints.py,sha256=jMeCnyJpK6iml-4JjRG92SymK7Z2i2pzEL4gwj8X6vI,1518
nipype/interfaces/ants/tests/test_auto_Atropos.py,sha256=D26cpWXa3QxYMKhemyxpT0X15ThkB3FobOUi59ouYhs,2867
nipype/interfaces/ants/tests/test_auto_AverageAffineTransform.py,sha256=MsEaPsRToW5A_9FhSnC11jeaVCAgdybKKd1ZkG55IGg,1373
nipype/interfaces/ants/tests/test_auto_AverageImages.py,sha256=wkzx02rPzD4BAxNq8UCNpK6_wC_2oA7q1y7UrLKP-kE,1468
nipype/interfaces/ants/tests/test_auto_BrainExtraction.py,sha256=b_wDvXM3mYIzGFJmS18Zi9K9j8Ll9TYUSNHBtaJGE-4,3401
nipype/interfaces/ants/tests/test_auto_ComposeMultiTransform.py,sha256=qp-XzEc8YNQlmE27kKzfYqvHoy1VeNKxSHwhJ0zTh2A,1567
nipype/interfaces/ants/tests/test_auto_CompositeTransformUtil.py,sha256=RQ3EJPDEN4nKdhtrTx5wj7cY-jc0QuHFwGZ9TNQZdmI,1591
nipype/interfaces/ants/tests/test_auto_ConvertScalarImageToRGB.py,sha256=uOz-f0yl9Qm8sJRlVVrab15DSOol20gpBYHbTF6s6oE,2228
nipype/interfaces/ants/tests/test_auto_CorticalThickness.py,sha256=Jg5TCmoJMck1FKp48lPDFZTgKgxNDn4lYXuX2zkMmkE,3785
nipype/interfaces/ants/tests/test_auto_CreateJacobianDeterminantImage.py,sha256=CkEjHAsLNt4kFJljpifmoW-a-h4qwmG3X6znzlPbXZM,1614
nipype/interfaces/ants/tests/test_auto_CreateTiledMosaic.py,sha256=-1F_l0kPLLtP5dY6UMwu-BR2uE0CbU5p8thC2_bZdp8,1851
nipype/interfaces/ants/tests/test_auto_DenoiseImage.py,sha256=AkbETVE1hEWkhj6HLT0LVYbFcHFTnYv67xK7SOgs88w,2026
nipype/interfaces/ants/tests/test_auto_GenWarpFields.py,sha256=bTM9U6M5FATt_-R64sDJqiDWZ0aVs-zgWiG5C-Sj4lE,2283
nipype/interfaces/ants/tests/test_auto_ImageMath.py,sha256=WoWz83ZD49S0gSViIiu24W1yyHmUh16mWvOlAv1uljw,1675
nipype/interfaces/ants/tests/test_auto_JointFusion.py,sha256=opASO9nLtoPoUciH4I4KK9Dhtf8r0uLZXN8ARkkAI50,3071
nipype/interfaces/ants/tests/test_auto_KellyKapowski.py,sha256=Pc4NedhHaLjom_Mj6At07XQuAcNa638T-0HTMa_Iepg,3356
nipype/interfaces/ants/tests/test_auto_LabelGeometry.py,sha256=iIXO5dzzbrrgGVOV5X1-oH3eQmZLF0BKbKuwhfdr5ic,1538
nipype/interfaces/ants/tests/test_auto_LaplacianThickness.py,sha256=fuGclOR7soo7B0xkWqQIxWbCBHcaQB5__3CEd9k9hko,2114
nipype/interfaces/ants/tests/test_auto_MeasureImageSimilarity.py,sha256=zkbeAvujg4Qe91nrONbD1DzHdoMCCvthhumvD2KPwW8,1965
nipype/interfaces/ants/tests/test_auto_MultiplyImages.py,sha256=FDbrHqWhUoYKCMGD1lwqZUDIl_jgCaLoVvdgxxahqJs,1479
nipype/interfaces/ants/tests/test_auto_N4BiasFieldCorrection.py,sha256=vQAf1l_8SRqvfbkc5CQN2nzJINKEw6inChYGM7mvLVs,2678
nipype/interfaces/ants/tests/test_auto_Registration.py,sha256=nNV6uCaiAO0YqoWa6L62zN07WkEOifXUFF6OGAIzHGY,6067
nipype/interfaces/ants/tests/test_auto_RegistrationSynQuick.py,sha256=WHkx1z9KYf0ho6YeS5Y8DWPtSyGumxeGl_3qhJYenpk,2215
nipype/interfaces/ants/tests/test_auto_ResampleImageBySpacing.py,sha256=N6PT4h9UuZMvrNud3QMmNUylH9J6Cl5sryqv49MJPHU,1922
nipype/interfaces/ants/tests/test_auto_ThresholdImage.py,sha256=hm3KORFG1PJXTN4eZj0s2K_uFdQ1_lun06COhXVEQu4,2366
nipype/interfaces/ants/tests/test_auto_WarpImageMultiTransform.py,sha256=fnP2ujGYbLn4RKZxi5RsBRAAhlCRSq_pGuuwaYASmxc,2253
nipype/interfaces/ants/tests/test_auto_WarpTimeSeriesImageMultiTransform.py,sha256=kqoJGgmgWHqpuvOXS3K8eJSt1FVrHD57zZHLKONHy90,2036
nipype/interfaces/ants/tests/test_auto_antsIntroduction.py,sha256=VwsDQt-ukADyZpdlJitA9oitwFFNT51AJpaFUbmvZ0c,2298
nipype/interfaces/ants/tests/test_auto_buildtemplateparallel.py,sha256=WUkR4SyZBCmfOGO1Mh3vMF2b2l2IDfoGIGjUCJqMm38,2199
nipype/interfaces/ants/tests/test_base.py,sha256=ieRADeNxJwricCf52xK2J38DCQsQc53Z_0ujl5idTtg,552
nipype/interfaces/ants/tests/test_extra_Registration.py,sha256=ZJ31UoY1xhU5VHii2g0NW4u7TKTiAwClKshRwS5Y2WQ,750
nipype/interfaces/ants/tests/test_resampling.py,sha256=wTWDMbkB26VimKq-ZrWYLDJE7Z-TSmbfrKMEz-gcmp8,3388
nipype/interfaces/ants/tests/test_segmentation.py,sha256=AHNRzLT3LezuYshXmvBaIyC7-BAzH7lCfSrAm6se6xQ,2051
nipype/interfaces/ants/utils.py,sha256=9NMvN3eVy2M-uWMyrHzSTVxiB43JDkG9HNq71SZJuOI,28110
nipype/interfaces/ants/visualization.py,sha256=t-Fr6UkUdGwJUJz7XQFAqdbPi3Qn5ZvXV6kvuSE1BZM,7115
nipype/interfaces/base/__init__.py,sha256=WdK63THqbiD-l1TDxRHa1WSIqQR0njdcsbBwIWRcVf0,1074
nipype/interfaces/base/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/base/__pycache__/core.cpython-311.pyc,,
nipype/interfaces/base/__pycache__/specs.cpython-311.pyc,,
nipype/interfaces/base/__pycache__/support.cpython-311.pyc,,
nipype/interfaces/base/__pycache__/traits_extension.cpython-311.pyc,,
nipype/interfaces/base/core.py,sha256=j-aJVDV0whelvZj8QW-1r7I6KIUnY0qmflwuYouVAkY,38055
nipype/interfaces/base/specs.py,sha256=IghVODEQDiCOuxs5ZwYVN-LMrYOK-JBRBoJyVAIZrCo,15175
nipype/interfaces/base/support.py,sha256=sZO4O6oC-edujVleXIXVwi1JrABwFmhqqKYB-SiccZo,16233
nipype/interfaces/base/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/base/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_BaseInterface.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_CommandLine.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_LibraryBaseInterface.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_MpiCommandLine.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_SEMLikeCommandLine.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_SimpleInterface.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_auto_StdOutCommandLine.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_core.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_resource_monitor.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_specs.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_support.cpython-311.pyc,,
nipype/interfaces/base/tests/__pycache__/test_traits_extension.cpython-311.pyc,,
nipype/interfaces/base/tests/test_auto_BaseInterface.py,sha256=4oreS5OMBZGMfFJ3C6h3PaTh6o9cevgmyVNv_oSVDB0,357
nipype/interfaces/base/tests/test_auto_CommandLine.py,sha256=AUlc3jFXSqU7TTkjaSay4yBRQymC7D99tn1lhJwl-zE,498
nipype/interfaces/base/tests/test_auto_LibraryBaseInterface.py,sha256=cJKoHzF9nKikmLtuKLru937MKMW1TgdKWNkrSn-36xo,378
nipype/interfaces/base/tests/test_auto_MpiCommandLine.py,sha256=ozV5CQ1a_jU4Hj7NSh2OYaUSClRnPHzii2VHrs34zjE,593
nipype/interfaces/base/tests/test_auto_SEMLikeCommandLine.py,sha256=q3epSozk-YnDzDdAXB1cDiGZW3qZUMBx_lmOLhGYleQ,519
nipype/interfaces/base/tests/test_auto_SimpleInterface.py,sha256=wSpO6KK15U5h-9VP6nr1MJVzRBfdVe17cCoOr7n-T_o,363
nipype/interfaces/base/tests/test_auto_StdOutCommandLine.py,sha256=C5O3JdHL80dhSHQSqW7Dc_o3NDxNQE48T94Htjyvehw,657
nipype/interfaces/base/tests/test_core.py,sha256=PevbPoa6OBygj3_71IFUCleGYPqKxvfMVR-7qtJUFcw,18483
nipype/interfaces/base/tests/test_resource_monitor.py,sha256=C3NU7SuPYAOa3as16aXLplrsHk_tiNStSYSfeEd5P5E,2977
nipype/interfaces/base/tests/test_specs.py,sha256=ZJ8dM6-Ne4CGbdAQZQTDxcmzXzCtLXzmagWQyPpTynE,15028
nipype/interfaces/base/tests/test_support.py,sha256=H7jEcd6ivINd6WZQwcW0pbmu8m6LhU1VJxAhozdFAzw,1521
nipype/interfaces/base/tests/test_traits_extension.py,sha256=KDedTzo_y_JQiKQDnKAmuLLFsCk65nfImZ93y9t_Kyc,10542
nipype/interfaces/base/traits_extension.py,sha256=AsMJk8Na7c3ZsqMceWnq81yFMZPngN8362ZnTlOI26c,17784
nipype/interfaces/brainsuite/__init__.py,sha256=9gnGSaQRJCf3TJsMgzEne2aFrf4XT62-GDTLUTqMB9s,209
nipype/interfaces/brainsuite/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/brainsuite/__pycache__/brainsuite.cpython-311.pyc,,
nipype/interfaces/brainsuite/brainsuite.py,sha256=XUr5gaHVAnY9xvFrEVzi1sBrryTwJIFYJWyOeeLS6i8,75367
nipype/interfaces/brainsuite/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/brainsuite/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_BDP.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Bfc.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Bse.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Cerebro.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Cortex.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Dewisp.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Dfs.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Hemisplit.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Pialmesh.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Pvc.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_SVReg.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Scrubmask.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Skullfinder.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_Tca.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/__pycache__/test_auto_ThicknessPVC.cpython-311.pyc,,
nipype/interfaces/brainsuite/tests/test_auto_BDP.py,sha256=P7x3I9FgoeTeN2KQMQcrHpHR2kRBj6q_iOZo5_SRK5w,5192
nipype/interfaces/brainsuite/tests/test_auto_Bfc.py,sha256=9xrW5-7tn-79JR89Q29qla2I5HrO0LFsymLaA8iz-es,3112
nipype/interfaces/brainsuite/tests/test_auto_Bse.py,sha256=0QXrcfIBb02cXpTsbW0R_8b6HJ3mKsfNr2U33AvDwyQ,2953
nipype/interfaces/brainsuite/tests/test_auto_Cerebro.py,sha256=amZYUIbgkjcoJMqxYiLDIQx6tehXUNAEwaiQMeBqLiY,2785
nipype/interfaces/brainsuite/tests/test_auto_Cortex.py,sha256=14MVsJigRU7gIpiz-g9BgKKINAY8jWPFHUCMQ56XDwM,1726
nipype/interfaces/brainsuite/tests/test_auto_Dewisp.py,sha256=elMAorF-Ym3CV0fOi2hbbZcbnYg5fjrjC1o0y5ztmvo,1334
nipype/interfaces/brainsuite/tests/test_auto_Dfs.py,sha256=FQ3B02Bj6cQRFe35IAMMmPRZ4CmhbiO2SUztZe-GsO0,2288
nipype/interfaces/brainsuite/tests/test_auto_Hemisplit.py,sha256=26Gq39i23VZh84DyhDLidw1Zikq7e53pBg_l56G8dEE,2102
nipype/interfaces/brainsuite/tests/test_auto_Pialmesh.py,sha256=cL2HcStSFSaiP7z9wRu0kCT0Myq6Ke1ks4ABY2TaQBo,2476
nipype/interfaces/brainsuite/tests/test_auto_Pvc.py,sha256=QaMZky7oSmygKh16APs_aaLl7RfYGktgMoFKDM4QED8,1621
nipype/interfaces/brainsuite/tests/test_auto_SVReg.py,sha256=kge0nVi5WMlo9IPZwz2BrFrcb4JbG2FYCGZ2iLXX7xk,2204
nipype/interfaces/brainsuite/tests/test_auto_Scrubmask.py,sha256=Chf0lghDVA3ZqEcQjo0u3_FqmdgNWilqurEbvShf9eY,1493
nipype/interfaces/brainsuite/tests/test_auto_Skullfinder.py,sha256=gpsSummHjqyXCcXl7Kdyhe-joWIMEqnZ0BH_6b7rgg8,1963
nipype/interfaces/brainsuite/tests/test_auto_Tca.py,sha256=sCVYuRsE757YV0YZRAJNvH4-3vZFy62zAKfDAz7RXbo,1455
nipype/interfaces/brainsuite/tests/test_auto_ThicknessPVC.py,sha256=yOo3v4SVPh1GJ-wut6_i4xIo54q_6z4ib-T7Zpmo-O4,603
nipype/interfaces/bru2nii.py,sha256=9Jnac3LJuPbLvPQPTFqmhIyEQxmMvC_BqiU9f6byH9U,2254
nipype/interfaces/c3.py,sha256=oFhknTIny-1qvMlkkeeIjlTHqqYf04X98pIJbgOg_Ps,7472
nipype/interfaces/camino/__init__.py,sha256=EIzSsvCmCL8PafrAxFMLaai0KC32thabJz3wikArHAk,837
nipype/interfaces/camino/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/calib.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/connectivity.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/convert.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/dti.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/odf.cpython-311.pyc,,
nipype/interfaces/camino/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/camino/calib.py,sha256=IyBZxx2inyP8CHkCNuHwDOubSB1PLJfpLFYEOhwuYs4,11412
nipype/interfaces/camino/connectivity.py,sha256=O3megpp45CRwUxCEj7MvgumFBO8V_kEOjVg49LvXEEs,6241
nipype/interfaces/camino/convert.py,sha256=wddF2aUJaJ4GDbR4rb3hbkkbi9uDp_tYiUkqRzwBp30,32141
nipype/interfaces/camino/dti.py,sha256=gH-H40yDY9PXglSY_KzEQJ7hyB8To-npIw4X16wROHg,50577
nipype/interfaces/camino/odf.py,sha256=JHy1zUFAt3hooRczKn_03HIyfz1r0n7pCNiZSI0Cpgw,22587
nipype/interfaces/camino/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/interfaces/camino/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_AnalyzeHeader.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ComputeEigensystem.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ComputeFractionalAnisotropy.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ComputeMeanDiffusivity.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ComputeTensorTrace.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_Conmat.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_DT2NIfTI.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_DTIFit.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_DTLUTGen.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_DTMetric.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_FSL2Scheme.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_Image2Voxel.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ImageStats.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_LinRecon.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_MESD.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ModelFit.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_NIfTIDT2Camino.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_PicoPDFs.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_ProcStreamlines.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_QBallMX.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_SFLUTGen.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_SFPICOCalibData.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_SFPeaks.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_Shredder.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_Track.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackBallStick.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackBayesDirac.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackBedpostxDeter.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackBedpostxProba.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackBootstrap.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackDT.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TrackPICo.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_TractShredder.cpython-311.pyc,,
nipype/interfaces/camino/tests/__pycache__/test_auto_VtkStreamlines.cpython-311.pyc,,
nipype/interfaces/camino/tests/test_auto_AnalyzeHeader.py,sha256=EgBLozvPjfbMFli2wTskTbV9EGkVaTXQQDAB2FM1Q78,3221
nipype/interfaces/camino/tests/test_auto_ComputeEigensystem.py,sha256=xvW8n5BcW_YtlHAufc_fVUIVakp-XqanNP_F_E5SEFo,1521
nipype/interfaces/camino/tests/test_auto_ComputeFractionalAnisotropy.py,sha256=Eue2DYHA8JCt2wwSe8rmVm3B_9X2LAeM5Q4ZSpfyCR8,1541
nipype/interfaces/camino/tests/test_auto_ComputeMeanDiffusivity.py,sha256=DBV7SQMD3TLXe2p4AQWAbo9llUGXAGuf9p-vPWEA7JU,1516
nipype/interfaces/camino/tests/test_auto_ComputeTensorTrace.py,sha256=Ed_s8foGFoYzI-dMvshZSpwJZpT84no1ZeIEV8OU9UU,1499
nipype/interfaces/camino/tests/test_auto_Conmat.py,sha256=FzCCz_0dN5TvLcsrWayzq5NPsY0YGRCOJ-aVcyRm6lc,1817
nipype/interfaces/camino/tests/test_auto_DT2NIfTI.py,sha256=_bkBNieUZx7Oj2S4mUZX3YzhuQNssw6Q4QnEo3AUOkc,1396
nipype/interfaces/camino/tests/test_auto_DTIFit.py,sha256=ntU67ZGHk1uVJOEUvphg9OrO-4ke55Z59jMn5Yr7hZQ,1427
nipype/interfaces/camino/tests/test_auto_DTLUTGen.py,sha256=b2mgtlhQuRVT8LFdCdso7GhloeZcIz3czyGRqLj0wX8,1964
nipype/interfaces/camino/tests/test_auto_DTMetric.py,sha256=5_F0A8u8GHwCyoksJfC1NQnXuNFAqjKNu1Fbcrt_6mc,1491
nipype/interfaces/camino/tests/test_auto_FSL2Scheme.py,sha256=xoIQqCU7YfbnM36w1CXZVsEAAqQpGVpWWXo0h7Bxxwc,1882
nipype/interfaces/camino/tests/test_auto_Image2Voxel.py,sha256=yHLuNelBJC4aDturrjh8SYzJF1RD_ql1F57vJqzG8P0,1261
nipype/interfaces/camino/tests/test_auto_ImageStats.py,sha256=kR1dvuGuwwPN8Imp7pGYLhAwNnM06RpGT6gbKkOJm5w,1302
nipype/interfaces/camino/tests/test_auto_LinRecon.py,sha256=L9dN12zwk3N1Tugs7dO-kWrh_rLdHTsKDl6gDidX6jU,1606
nipype/interfaces/camino/tests/test_auto_MESD.py,sha256=j4WdltVmG2j_gYkskd3M4WkMlCKPrIBdRssCESEU5yY,1858
nipype/interfaces/camino/tests/test_auto_ModelFit.py,sha256=NwEM8lgiCfblhpu2aFNZMiV8kRQbQw0tR0SmYgTKyio,2296
nipype/interfaces/camino/tests/test_auto_NIfTIDT2Camino.py,sha256=iV5gXJa15CVeWYPE0XzaU2DsrosrHQOc0gWHr4MGq5E,1656
nipype/interfaces/camino/tests/test_auto_PicoPDFs.py,sha256=-M34LCfBvoGcdIeHcJS2HDxXuN8-2BsvijJSS4F4Qj4,1687
nipype/interfaces/camino/tests/test_auto_ProcStreamlines.py,sha256=bEjtxlmiUuZzwzuTFz4k_p0qVRcXNNiThDBy0bt2haQ,4127
nipype/interfaces/camino/tests/test_auto_QBallMX.py,sha256=HyrgEXv0P8zkrYa8_4QnOIXmAc5M_DhwvhmlEHAm-ag,1569
nipype/interfaces/camino/tests/test_auto_SFLUTGen.py,sha256=tO4vn4XYahKn_7dqu7ySZrI8p-9frVmH-b1_ZHBb1is,1839
nipype/interfaces/camino/tests/test_auto_SFPICOCalibData.py,sha256=ergFcLK3tYVEmiTtvFOZGkz-MDCIiOry23Snl7_LpaY,2453
nipype/interfaces/camino/tests/test_auto_SFPeaks.py,sha256=L0nFFI53t9QCXS5zAG0o8ZVugU5g5EoccqGygacLwyc,2210
nipype/interfaces/camino/tests/test_auto_Shredder.py,sha256=k0-gbpWMjKgA7y8-dKoT_g5IYMdZLoJupex8h-gMUSE,1426
nipype/interfaces/camino/tests/test_auto_Track.py,sha256=TltjhwExLUjPLbamQm9TqPxDga5qxNEVkBjeuc_TDDI,2740
nipype/interfaces/camino/tests/test_auto_TrackBallStick.py,sha256=B5NsQ5QXplcd3A-tS3Ol69J0R5opVxVqwAB81KtRyVc,2785
nipype/interfaces/camino/tests/test_auto_TrackBayesDirac.py,sha256=8Bh0tMclVX2IC29FemjFjycgqM7JzrJr50cq7VenOsg,3558
nipype/interfaces/camino/tests/test_auto_TrackBedpostxDeter.py,sha256=dxagCPhLqiSRdOl7VrnXJsUNkZoWLNBlZFW2eNkDA2o,3009
nipype/interfaces/camino/tests/test_auto_TrackBedpostxProba.py,sha256=MOBxyhke_Mv3HprlDPcJw0toR4hFQ8N0gxCwMlLVz3g,3106
nipype/interfaces/camino/tests/test_auto_TrackBootstrap.py,sha256=O49MXtJNvLDFiNZlrnlKHZ9-fXEK6rddxH8XQg-wraw,3280
nipype/interfaces/camino/tests/test_auto_TrackDT.py,sha256=50PjAARdPgOq-ki6psNw7yziKSMpk9s4qtLsT_Dv9M8,2750
nipype/interfaces/camino/tests/test_auto_TrackPICo.py,sha256=ixCGJipTLNV0NAH3kyG9FCATnvhcqOTlpJR_GSgsTEA,2916
nipype/interfaces/camino/tests/test_auto_TractShredder.py,sha256=uydoXS6kCdy4cGQcod7_NXa_HyW7b5TwbkFXk7ZP_fQ,1451
nipype/interfaces/camino/tests/test_auto_VtkStreamlines.py,sha256=mID6z-8PhtV-HyVGxAS1_ysYlAGu_voOpL1guclh2KQ,1965
nipype/interfaces/camino/utils.py,sha256=Wex8BUJqKfeRbqAyX8KnUR_1V-xRkbUuv8nuGTJvzgk,2448
nipype/interfaces/camino2trackvis/__init__.py,sha256=1uYUGl5PK56X2aP9goHRJZYh_4a6AvI9sXhTDC2Nfsw,243
nipype/interfaces/camino2trackvis/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/camino2trackvis/__pycache__/convert.cpython-311.pyc,,
nipype/interfaces/camino2trackvis/convert.py,sha256=-gKJ_lhDE0Fj9fWFR5f8HpJ7xP68HqkYxSdnl6p_7Fw,5077
nipype/interfaces/camino2trackvis/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/camino2trackvis/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/camino2trackvis/tests/__pycache__/test_auto_Camino2Trackvis.cpython-311.pyc,,
nipype/interfaces/camino2trackvis/tests/__pycache__/test_auto_Trackvis2Camino.cpython-311.pyc,,
nipype/interfaces/camino2trackvis/tests/test_auto_Camino2Trackvis.py,sha256=MBkbRpYA54pStQStwrvTtY8v7aLq4X1KEr-BP1QAxno,1808
nipype/interfaces/camino2trackvis/tests/test_auto_Trackvis2Camino.py,sha256=Y9Q3XpCcPaa0VGtQ-EWHCYHU2_eBL5JJy4zI2tCtbeU,1260
nipype/interfaces/cat12/__init__.py,sha256=R4o38CbiHVzZ_0qtew8gys7NBhXQ7bofY_2h30rFH0s,159
nipype/interfaces/cat12/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/cat12/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/cat12/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/cat12/__pycache__/surface.cpython-311.pyc,,
nipype/interfaces/cat12/base.py,sha256=r_dFgOCi7KwLD8x1w9KkdZ8oKtyS42IWRteC-MwLIvM,335
nipype/interfaces/cat12/preprocess.py,sha256=qYenD7OcE5DfoaIi0fWQ86g4qETLa_ZQj1zrbevboCs,32137
nipype/interfaces/cat12/surface.py,sha256=8CcueGV2aa_DeqicR9-D7pkShaZc3C2AiCZkoBNTY-c,10352
nipype/interfaces/cat12/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/cat12/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/cat12/tests/__pycache__/test_auto_CAT12SANLMDenoising.cpython-311.pyc,,
nipype/interfaces/cat12/tests/__pycache__/test_auto_CAT12Segment.cpython-311.pyc,,
nipype/interfaces/cat12/tests/__pycache__/test_auto_ExtractAdditionalSurfaceParameters.cpython-311.pyc,,
nipype/interfaces/cat12/tests/__pycache__/test_auto_ExtractROIBasedSurfaceMeasures.cpython-311.pyc,,
nipype/interfaces/cat12/tests/test_auto_CAT12SANLMDenoising.py,sha256=1Ijvw-i95rH4dK5z6_dYt10eAuw8BKj0pxdsDdAcww4,1875
nipype/interfaces/cat12/tests/test_auto_CAT12Segment.py,sha256=e-WIJ3rVSjYdhrFL4Hta4jevCH1v0G9OlrJLQ88AXCU,7515
nipype/interfaces/cat12/tests/test_auto_ExtractAdditionalSurfaceParameters.py,sha256=oght5-0XluF8KP08S_UNPggRZeXcNdlB54sjZjRxQc0,1995
nipype/interfaces/cat12/tests/test_auto_ExtractROIBasedSurfaceMeasures.py,sha256=Ef7k0Od2w1_rvxcXvHfxeDhD9Ez8hGXArUGm2Sx3DpY,1524
nipype/interfaces/cmtk/__init__.py,sha256=73wKjXQcwgKyDwFup9UQxhstDnvNxCrxeEEzEpcK2sU,317
nipype/interfaces/cmtk/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/cmtk.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/convert.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/nbs.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/nx.cpython-311.pyc,,
nipype/interfaces/cmtk/__pycache__/parcellation.cpython-311.pyc,,
nipype/interfaces/cmtk/base.py,sha256=HnJ6DDuxGF3bjnGRkLWtdin1k7ICq5mJPyFt--myvAk,703
nipype/interfaces/cmtk/cmtk.py,sha256=C4Qwr9fMK6nCHdE6mflwuUMx2zGUOWh1FynNz2aTxXc,40897
nipype/interfaces/cmtk/convert.py,sha256=cquItMVdjmVIF72ihLAKNNr9zH6FB3RJhrSO-BRKAtg,10234
nipype/interfaces/cmtk/nbs.py,sha256=zhjhpaxp_lGOsNXuKXFpPUdCe-rWfA0ds87jjB8qaWU,6845
nipype/interfaces/cmtk/nx.py,sha256=gsSHbHZo8YVoYgBYDyCreHu1NLM8-QxBWl1vv45HmGE,26521
nipype/interfaces/cmtk/parcellation.py,sha256=ss6zDW1CxKTzNCF25yL6rdI_RqWmRnEjQ7NNKUZ5e6w,28208
nipype/interfaces/cmtk/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/cmtk/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_AverageNetworks.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_CFFBaseInterface.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_CFFConverter.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_CreateMatrix.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_CreateNodes.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_MergeCNetworks.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_NetworkBasedStatistic.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_NetworkXMetrics.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_Parcellate.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_auto_ROIGen.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/__pycache__/test_nbs.cpython-311.pyc,,
nipype/interfaces/cmtk/tests/test_auto_AverageNetworks.py,sha256=qnGbjBys3Ykea7aym_oHXyPZcdwMfSYPZhYHV5iv9M8,1175
nipype/interfaces/cmtk/tests/test_auto_CFFBaseInterface.py,sha256=LAbC1GVo0GeJg7GXuo95-2-8wgmMqSrIKu_YJDWmQHE,366
nipype/interfaces/cmtk/tests/test_auto_CFFConverter.py,sha256=gHvtJYRMIE7zGU-AvPck9W4vLtlNRR_0lMyONHGWnAI,1398
nipype/interfaces/cmtk/tests/test_auto_CreateMatrix.py,sha256=NTXkYd_OYkE1FxMhbZ1JRjHwVu7n_B1HacsvTQ9sIQI,3041
nipype/interfaces/cmtk/tests/test_auto_CreateNodes.py,sha256=57ZLNt3lSbOZNEkagoj5_5DldAFQbcl6Bi13mewtDM4,993
nipype/interfaces/cmtk/tests/test_auto_MergeCNetworks.py,sha256=GojpVFLO2fi0fCXR3cj3fOvxLNZ3X9t6xm0F9Tx1Mfw,875
nipype/interfaces/cmtk/tests/test_auto_NetworkBasedStatistic.py,sha256=ILJmZOtzBiNZDrDmcBu82yVQ6LtpZIs25eMD6ZAJCfk,1461
nipype/interfaces/cmtk/tests/test_auto_NetworkXMetrics.py,sha256=_cc6yiihSnBVqHgdvEYaPXohGQ3haWYZp2LHfgVUyWw,2317
nipype/interfaces/cmtk/tests/test_auto_Parcellate.py,sha256=nvgvKAsGtJe85F-16M_jNhFpc6o18fl0iraVai4aONg,1566
nipype/interfaces/cmtk/tests/test_auto_ROIGen.py,sha256=2TvaSFplnx4E2N3E_klaWVbOk_TL0EJ8wtMCFMZGmtM,1282
nipype/interfaces/cmtk/tests/test_nbs.py,sha256=IufE8zqthjTCd-f1jL8n2mAwWAXTns39PnFdozTw_pQ,1616
nipype/interfaces/dcm2nii.py,sha256=5K0cl63bZyD0CLOyiZyOjZXXmeHyDSOViYFYl2j_l9M,17563
nipype/interfaces/dcmstack.py,sha256=ISPAnf_5Rer1JW6ekrYHeNh7-7ldI4qC6IEypsHHAa0,14043
nipype/interfaces/diffusion_toolkit/__init__.py,sha256=Yn7btnvx_oApt1XK0hPmzG0xEGfbkvNwv3qTS-vtr6A,253
nipype/interfaces/diffusion_toolkit/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/__pycache__/dti.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/__pycache__/odf.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/__pycache__/postproc.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/base.py,sha256=bBnSooArVJvP3z9zf_iTtENzTWf_jaFrEGfvtdqMpOM,1256
nipype/interfaces/diffusion_toolkit/dti.py,sha256=aqpekBZfcql5748FM2AAixum-iWmxYGrMKbaGM2l5S4,9784
nipype/interfaces/diffusion_toolkit/odf.py,sha256=DwG0UNn6sk4r2yMS74tq5vOffW8XWyM6k_N8ylMYYlU,13637
nipype/interfaces/diffusion_toolkit/postproc.py,sha256=JTaf62aptV9CVEY0SjLOJDWE2oGpi5ujTDIOdnPI_7M,3361
nipype/interfaces/diffusion_toolkit/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/diffusion_toolkit/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_DTIRecon.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_DTITracker.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_HARDIMat.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_ODFRecon.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_ODFTracker.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_SplineFilter.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/__pycache__/test_auto_TrackMerge.cpython-311.pyc,,
nipype/interfaces/diffusion_toolkit/tests/test_auto_DTIRecon.py,sha256=xMiope8QrtJmnQI-Fbcu-VeGfBF7wE1p7UQ4kFnlJ_Q,2288
nipype/interfaces/diffusion_toolkit/tests/test_auto_DTITracker.py,sha256=86bPxhrJUqe0D3U-BYREtERmf0FIN8McBiGhaJATq5M,2557
nipype/interfaces/diffusion_toolkit/tests/test_auto_HARDIMat.py,sha256=XWG1b_BxOh3MJ5jB3Y_9To0I5bLZQ36zk3pwb5FQmls,1690
nipype/interfaces/diffusion_toolkit/tests/test_auto_ODFRecon.py,sha256=FDGLknGC9BCDsnNPXp17yagRBvtGEGsWLiv5hdyEIzk,2303
nipype/interfaces/diffusion_toolkit/tests/test_auto_ODFTracker.py,sha256=z0wCQS77D7xBQh7EtOW0zLHGmjlMe3G5L9rcxvzu0aM,2789
nipype/interfaces/diffusion_toolkit/tests/test_auto_SplineFilter.py,sha256=mM2i5w1HVYUMKzMHdmvKKMVpG9qpYZeXWVu3VOnPmlk,1258
nipype/interfaces/diffusion_toolkit/tests/test_auto_TrackMerge.py,sha256=-wGimnatYZJOafr8zxtSfI3O2LFuloCxWBpVWh8HDDw,1101
nipype/interfaces/dipy/__init__.py,sha256=0LA53DwaddBC_Ve8EQkgrmcdzsbTmZSAV7RbZdBrgA8,350
nipype/interfaces/dipy/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/anisotropic_power.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/reconstruction.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/setup.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/simulate.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/stats.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/tensors.cpython-311.pyc,,
nipype/interfaces/dipy/__pycache__/tracks.cpython-311.pyc,,
nipype/interfaces/dipy/anisotropic_power.py,sha256=5JXkVWJ6BD7vVwjo5sQHrrJsXrD-hdOnKCveW7utKkQ,2090
nipype/interfaces/dipy/base.py,sha256=mU1fgu1cMfAiKVsM266FIDBGf2t3hkE9YAnrHG__dVg,8339
nipype/interfaces/dipy/preprocess.py,sha256=ipeCT7kir3kBv0vDmK2M0QFvXe63AqldBu0Wt4T3DV4,9741
nipype/interfaces/dipy/reconstruction.py,sha256=8SqBNHPRP8kzZMeWAQAKAcRtMZma7JmzmnlNtd_VH4M,13467
nipype/interfaces/dipy/registration.py,sha256=6MnlJIZQdYf6bHQ39tc9nWVZFOxHRZt30dYQW3YXbUE,650
nipype/interfaces/dipy/setup.py,sha256=-C8TLAr3quOp69wVA74W34Z-D5TQEauHqY3WTFS1oXA,463
nipype/interfaces/dipy/simulate.py,sha256=PzLD1_Sjedz-u8jw3zRCX-1qAY43oC57eBf56ynzsBs,11564
nipype/interfaces/dipy/stats.py,sha256=TLEUr4zUpUhfVCxNPtKrm6FZDH3x7ZgDIYlBMv1n98w,649
nipype/interfaces/dipy/tensors.py,sha256=jdSNN1hj111maqpdmnT3id7YHD3Wfl8EZ-KUwhrHdRQ,4635
nipype/interfaces/dipy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/dipy/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_APMQball.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_CSD.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_DTI.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_Denoise.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_DipyBaseInterface.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_DipyDiffusionInterface.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_EstimateResponseSH.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_RESTORE.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_Resample.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_SimulateMultiTensor.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_StreamlineTractography.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_TensorMode.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_auto_TrackDensityMap.cpython-311.pyc,,
nipype/interfaces/dipy/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/interfaces/dipy/tests/test_auto_APMQball.py,sha256=Mj24XOgypwali5Fpe3KmKbbrIuaTzVy-2ONuLZly1c0,1118
nipype/interfaces/dipy/tests/test_auto_CSD.py,sha256=2ydakGHv47GAkrT0zzddUymh7034FeeqfBih44RqcC4,1401
nipype/interfaces/dipy/tests/test_auto_DTI.py,sha256=AT_sEaiCYc3e7iYWuVrz4xo1HZNaMeQNnQHtReJK-BA,1399
nipype/interfaces/dipy/tests/test_auto_Denoise.py,sha256=uAt7cuqFH0_utH625_OWGNFgC5Zcn-LnxdIrHIvJrSo,1213
nipype/interfaces/dipy/tests/test_auto_DipyBaseInterface.py,sha256=PdxErk20XcFpGdJDf8C3m5-e0nIbF2Vo5sSK1-NXkyM,369
nipype/interfaces/dipy/tests/test_auto_DipyDiffusionInterface.py,sha256=wJRR-JwCM-7K6Pq4T7D8IdO_rxmguYLQRdt4yDErcLI,749
nipype/interfaces/dipy/tests/test_auto_EstimateResponseSH.py,sha256=fQy1UjMKUEoxAGOehziHD0LLBIcmGRjUj9t8I8i_AcQ,1752
nipype/interfaces/dipy/tests/test_auto_RESTORE.py,sha256=L7a9LGFeGWU6Ei2czDf1vSWHwIn5-XvHwpJ0al2BiuU,1520
nipype/interfaces/dipy/tests/test_auto_Resample.py,sha256=cLxkfVJvKBjuIlwPPQ8vF6MkkxYCGDILKOxjxP1hfWg,891
nipype/interfaces/dipy/tests/test_auto_SimulateMultiTensor.py,sha256=GBXqPNbE8zb6e3JQ8p7qEmKHGHF2TKLFyV-Q5p-SJEw,2190
nipype/interfaces/dipy/tests/test_auto_StreamlineTractography.py,sha256=OvN_VdhoV6VtPGnrXsUoOqHZBkLkLwa5hGIfsmFrelU,1939
nipype/interfaces/dipy/tests/test_auto_TensorMode.py,sha256=OoW9HlXS7tpubxcPkMDyXCTPGtfITzDKoq8SofSfpVo,1118
nipype/interfaces/dipy/tests/test_auto_TrackDensityMap.py,sha256=-RC2tSZkY2rKaUMNISPHaIK7XSskpaRkO4RC-z4CSH0,1088
nipype/interfaces/dipy/tests/test_base.py,sha256=0_L7q0XvEbhDbmEBwUv1U8PhBEn6r-iZfyauKcajWuo,7273
nipype/interfaces/dipy/tracks.py,sha256=LPtSY1R-75NlaWBF4Hg8-wATiwDNAPMYo4GdexYHAPY,12177
nipype/interfaces/dtitk/__init__.py,sha256=kSsLO4leYDPMXKPzYMBBKOLPp-OuMHelDH39R97pwGo,508
nipype/interfaces/dtitk/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/dtitk/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/dtitk/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/dtitk/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/dtitk/base.py,sha256=NZg6vF4EH25-7No2kktmrPzm0FouAQohiL_qtgJZDe8,3255
nipype/interfaces/dtitk/registration.py,sha256=qstagPXKQJPYm6C4IQeFEXbg1hhrTHu9rFQmoa1GnGs,18158
nipype/interfaces/dtitk/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/interfaces/dtitk/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_AffScalarVol.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_AffSymTensor3DVol.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_Affine.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_AffineTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_BinThresh.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_BinThreshTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_CommandLineDtitk.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_ComposeXfm.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_ComposeXfmTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_Diffeo.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_DiffeoScalarVol.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_DiffeoSymTensor3DVol.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_DiffeoTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_Rigid.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_RigidTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_SVAdjustVoxSp.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_SVAdjustVoxSpTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_SVResample.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_SVResampleTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVAdjustOriginTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVAdjustVoxSp.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVAdjustVoxSpTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVResample.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVResampleTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVtool.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_TVtoolTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_affScalarVolTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_affSymTensor3DVolTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_diffeoScalarVolTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/__pycache__/test_auto_diffeoSymTensor3DVolTask.cpython-311.pyc,,
nipype/interfaces/dtitk/tests/test_auto_AffScalarVol.py,sha256=5zLcSF7FblnRXBz649-o-QCwatn5E-TXI32fbmLawEE,1893
nipype/interfaces/dtitk/tests/test_auto_AffSymTensor3DVol.py,sha256=BMDZmht46pCzqbprZbMqrekJzAoLRsGEIR8hF-jHmgY,2016
nipype/interfaces/dtitk/tests/test_auto_Affine.py,sha256=Nd3D4_eREZKLMaHDExfAQAY35x1uetSy9MAy6UqVzec,1809
nipype/interfaces/dtitk/tests/test_auto_AffineTask.py,sha256=tGXT0VaFkGV0Ed-s50BrhYGcHowc6tM7l2qrFY9iOQ8,1829
nipype/interfaces/dtitk/tests/test_auto_BinThresh.py,sha256=1re4IAvehxAz2_uQUsP-0Bf5rOZ77PLmbNSKDWINEas,1762
nipype/interfaces/dtitk/tests/test_auto_BinThreshTask.py,sha256=jqGP8j_NiWVa8DzMxzo9xROodlkzG4ILlDbWKTwUeS8,1782
nipype/interfaces/dtitk/tests/test_auto_CommandLineDtitk.py,sha256=JrLqQsCd2RhJjA_zyDyB0acVul_NEQs9fAixz8bvvnY,513
nipype/interfaces/dtitk/tests/test_auto_ComposeXfm.py,sha256=TSMx9mRS3Kchv70Wppuktx6RprqKf9RXCYs3ObkTkGU,1196
nipype/interfaces/dtitk/tests/test_auto_ComposeXfmTask.py,sha256=d57jsP0iHIVretRVDw3RpEVlVaicZ1RpnZ7AIsfu8vA,1216
nipype/interfaces/dtitk/tests/test_auto_Diffeo.py,sha256=y7afFKcTos8JGLCSXr0ldVSbGBrJ_UIMJNuSvFuMY-0,1671
nipype/interfaces/dtitk/tests/test_auto_DiffeoScalarVol.py,sha256=dtrHr1gIGuX6qaK2uklDvvpuUxqms07sl33U4oG1jIs,1781
nipype/interfaces/dtitk/tests/test_auto_DiffeoSymTensor3DVol.py,sha256=w7pEjU87-xobg7t5f6R4MOBguagiw4vhxonZF0Kcz5I,1990
nipype/interfaces/dtitk/tests/test_auto_DiffeoTask.py,sha256=V8pAPppo7F8Y13hL6SgfaYxfk1Un115WbF-b_CX83Gg,1691
nipype/interfaces/dtitk/tests/test_auto_Rigid.py,sha256=V8DXIUQkThSotecXGhM3J5RDmBpUZe1dFHsqANW8FNI,1804
nipype/interfaces/dtitk/tests/test_auto_RigidTask.py,sha256=LWsq67wr-Bro0R63KLXqN20mbaKFnxPKoY_ReDkMmho,1824
nipype/interfaces/dtitk/tests/test_auto_SVAdjustVoxSp.py,sha256=vAcU1CnhZRfe6mx-u3mU523QMUNbC9ssXjsK9MY1sdU,1517
nipype/interfaces/dtitk/tests/test_auto_SVAdjustVoxSpTask.py,sha256=vNwa0tjveAisVOh6O7SZw0dDioO3PB6gDmphSJ1Re1E,1537
nipype/interfaces/dtitk/tests/test_auto_SVResample.py,sha256=sZ0uzAopayP6rEtZ7N4cgMP0xGvtVK-5P-JBkZumJK0,1691
nipype/interfaces/dtitk/tests/test_auto_SVResampleTask.py,sha256=YBa3M5OcoUv3XULUNlVaaz6YbecLcnx3YZdQSraNRwk,1711
nipype/interfaces/dtitk/tests/test_auto_TVAdjustOriginTask.py,sha256=fqu8N2QcCdfEg9EI-5HFd7biW_iArYJMRtruZBKrgxg,1542
nipype/interfaces/dtitk/tests/test_auto_TVAdjustVoxSp.py,sha256=gWulXpLPID8QvUXkas9zrxw0iTskCpH5aOdLkDkGGA4,1517
nipype/interfaces/dtitk/tests/test_auto_TVAdjustVoxSpTask.py,sha256=Izo2NAS9b050EhKmxH19JpN3Q3d-NbcodhPmcFljyO0,1537
nipype/interfaces/dtitk/tests/test_auto_TVResample.py,sha256=MeI_VGOy7ckvkhVgOxpN79E7Nc7xlR4djK9ZB6X_fao,1763
nipype/interfaces/dtitk/tests/test_auto_TVResampleTask.py,sha256=XNTHBgxlxmMIY0m7X4SzlnpZkf4L9M-PNyZgC5rJZ18,1783
nipype/interfaces/dtitk/tests/test_auto_TVtool.py,sha256=GUdeglNNeI90Fgy5hnOvUIsxNvDIPAAVZYqMXrSNsOo,1111
nipype/interfaces/dtitk/tests/test_auto_TVtoolTask.py,sha256=I0R1YiPi8DXtIz2tjRNa4dundCPfISrcxrIX0LF3Faw,1131
nipype/interfaces/dtitk/tests/test_auto_affScalarVolTask.py,sha256=ZTgKxlXGAUsFtkhMfvCpvCyG-b3na3TDJxbkQyjnixw,1913
nipype/interfaces/dtitk/tests/test_auto_affSymTensor3DVolTask.py,sha256=IdkeYd5xjmKS_1QKF0-tnLnp7jSt-FIntuia-Ym79aY,2036
nipype/interfaces/dtitk/tests/test_auto_diffeoScalarVolTask.py,sha256=JavjM_m25Ky2m6gg1xeJ_Jk97AxrQA73xzD9A8LUYeg,1801
nipype/interfaces/dtitk/tests/test_auto_diffeoSymTensor3DVolTask.py,sha256=L70NP4_Nw2Fx-vdIP_yW-kQk6JlJPrG6dc8RdAR94j8,2010
nipype/interfaces/dtitk/utils.py,sha256=T2D3Hnx0XtQSfp9r8jShRkDGG2YHvgJSwPeteRz2oww,11602
nipype/interfaces/dynamic_slicer.py,sha256=NBmxelSbbnF_E2lYtGfz8JwDIQ43t5vXQlW5pq6hh2s,8339
nipype/interfaces/elastix/__init__.py,sha256=UCKcj03oOVCxjCD0CwO5ePW4gAq-K6_Ogh0UkTgDxe4,296
nipype/interfaces/elastix/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/elastix/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/elastix/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/elastix/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/elastix/base.py,sha256=gAFckvbHKRPEXPeFfNEwWN_bpeV7cnXM67cdmOTnH9c,811
nipype/interfaces/elastix/registration.py,sha256=wRJfNtZNZjHMrR88GzXQwtdfOWs0950CLR9uFHxHasY,8523
nipype/interfaces/elastix/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/elastix/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/elastix/tests/__pycache__/test_auto_AnalyzeWarp.cpython-311.pyc,,
nipype/interfaces/elastix/tests/__pycache__/test_auto_ApplyWarp.cpython-311.pyc,,
nipype/interfaces/elastix/tests/__pycache__/test_auto_EditTransform.cpython-311.pyc,,
nipype/interfaces/elastix/tests/__pycache__/test_auto_PointsWarp.cpython-311.pyc,,
nipype/interfaces/elastix/tests/__pycache__/test_auto_Registration.cpython-311.pyc,,
nipype/interfaces/elastix/tests/test_auto_AnalyzeWarp.py,sha256=UrLmMi_3BCFrWhu6Lz34kvqJrIBfWDiznwzW5vc2KK0,1748
nipype/interfaces/elastix/tests/test_auto_ApplyWarp.py,sha256=lNUQ_CbqZzAOEaICFUU4amJ0T_Nv9RdB0arWAW0UaWk,1340
nipype/interfaces/elastix/tests/test_auto_EditTransform.py,sha256=cfZ6Zale7AKNK9SwrQ06JgBukR5vsg5eOa9nZMAd-bc,1223
nipype/interfaces/elastix/tests/test_auto_PointsWarp.py,sha256=A0_VRoke5HsPLK3wciuS3Zs1YkoGdI-h1wBot_r1CLw,1345
nipype/interfaces/elastix/tests/test_auto_Registration.py,sha256=ewgNjeHpN4Z2rXg1Jb8MS0lOYwcXG7RQY5dqHJm0_zs,1831
nipype/interfaces/elastix/utils.py,sha256=OogacgYjkdTDMlHaVd60joc9Ah3j5KaoIMarCQMCSCE,5692
nipype/interfaces/freesurfer/__init__.py,sha256=x44f7Wi-rKkGEBSCvVE49-uRah2j7WY_5EFCXQl0roY,1925
nipype/interfaces/freesurfer/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/longitudinal.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/model.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/petsurfer.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/freesurfer/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/freesurfer/base.py,sha256=yZmBRs7pl8B_YOk-3A268MVLMvuA_3AVZUWwHiJC9Y0,8188
nipype/interfaces/freesurfer/longitudinal.py,sha256=NLJDOf86Y0QoNG3kwcTSEEO2t_IAJ3VMYuyrdb3ljTA,9737
nipype/interfaces/freesurfer/model.py,sha256=F88Y285Xwv_-u1slmp4D1hi1f7ZRr-kmyR3zrOvNKgE,62697
nipype/interfaces/freesurfer/petsurfer.py,sha256=PrYOcWk5y9r4QKMeKrwtJOhaDb6xguIKaazMxbN2u_I,20939
nipype/interfaces/freesurfer/preprocess.py,sha256=qtIixe7QrhO6hCiiQVlf3OeJ26eH8ZwvC3l1WD-Q35Y,119304
nipype/interfaces/freesurfer/registration.py,sha256=Os-f9FJvk7pWLvQyl-Llmq3Zg2PVSgSCK7W3t1KoEp8,19982
nipype/interfaces/freesurfer/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/freesurfer/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_BBRegister.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_FSSurfaceCommand.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_AddXFormToHeader.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Aparc2Aseg.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Apas2Aseg.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ApplyMask.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ApplyVolTransform.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Binarize.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_CALabel.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_CANormalize.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_CARegister.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_CheckTalairachAlignment.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Concatenate.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ConcatenateLTA.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Contrast.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Curvature.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_CurvatureStats.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_DICOMConvert.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_EMRegister.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_EditWMwithAseg.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_EulerNumber.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ExtractMainComponent.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FSCommand.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FSCommandOpenMP.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FSScriptCommand.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FitMSParams.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FixTopology.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_FuseSegmentations.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_GLMFit.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_GTMPVC.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_GTMSeg.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ImageInfo.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Jacobian.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_LTAConvert.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Label2Annot.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Label2Label.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Label2Vol.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Logan.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MNIBiasCorrection.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MPRtoMNI305.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIConvert.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRICoreg.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIFill.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIMarchingCubes.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIPretess.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRISPreproc.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRISPreprocReconAll.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRITessellate.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsCALabel.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsCalc.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsCombine.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsConvert.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsExpand.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRIsInflate.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRTM1.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MRTM2.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MS_LDA.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MakeAverageSubject.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_MakeSurfaces.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Normalize.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_OneSampleTTest.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Paint.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ParcellationStats.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ParseDICOMDir.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_ReconAll.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Register.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RegisterAVItoTalairach.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RelabelHypointensities.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RemoveIntersection.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RemoveNeck.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Resample.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RobustRegister.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_RobustTemplate.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SampleToSurface.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SegStats.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SegStatsReconAll.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SegmentCC.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SegmentWM.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Smooth.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SmoothTessellation.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Sphere.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SphericalAverage.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Surface2VolTransform.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SurfaceSmooth.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SurfaceSnapshots.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SurfaceTransform.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_SynthesizeFLASH.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_TalairachAVI.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_TalairachQC.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_Tkregister2.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_UnpackSDICOMDir.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_VolumeMask.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_auto_WatershedSkullStrip.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_model.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_preprocess.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/__pycache__/test_utils.cpython-311.pyc,,
nipype/interfaces/freesurfer/tests/test_BBRegister.py,sha256=GadW3Dvl5YiO7wWveR2xAd7k2q8F1H_p-pzbYcY_2Ow,3119
nipype/interfaces/freesurfer/tests/test_FSSurfaceCommand.py,sha256=FVPHGe2Lz66tE6aoLzSoBFJnRXDlC5h6axP97estEiw,1561
nipype/interfaces/freesurfer/tests/test_auto_AddXFormToHeader.py,sha256=LclcBoYt345s8ulq-witTaqQqoVcSZ46D7B_DSMdQz4,1435
nipype/interfaces/freesurfer/tests/test_auto_Aparc2Aseg.py,sha256=aQqAy0Vf39vIiFdAVGvtz9ia93dsLoYixrkm9BnygEE,2567
nipype/interfaces/freesurfer/tests/test_auto_Apas2Aseg.py,sha256=J_NQCVNiEZ7nTAyNwd2SXyPqJunv8w2AzLOlfkXvf3w,1122
nipype/interfaces/freesurfer/tests/test_auto_ApplyMask.py,sha256=s2s6_hpsy084_HgggAvING86kfstdHYNH3KTYzwEALU,2044
nipype/interfaces/freesurfer/tests/test_auto_ApplyVolTransform.py,sha256=CnpUfiqQAVTNojEZtVU94VskeJU70PM5K4-iaZSt8IE,5377
nipype/interfaces/freesurfer/tests/test_auto_Binarize.py,sha256=U1vraGO7zk5hxSDh6sl_51cbxod48skHnAKuu8HKoOw,2936
nipype/interfaces/freesurfer/tests/test_auto_CALabel.py,sha256=uUtADfi8PrBUosSY8JT6kp_eIGoXPTwATfDsulY9aMc,2103
nipype/interfaces/freesurfer/tests/test_auto_CANormalize.py,sha256=M6y6mW40DmBzFQibHsckLHXzCO0nA3s23c-7J4nhS2g,1894
nipype/interfaces/freesurfer/tests/test_auto_CARegister.py,sha256=dv775VoDvDv-E-Nykk8wmN3G4M2KJq9MYVe8LxP4IuI,1901
nipype/interfaces/freesurfer/tests/test_auto_CheckTalairachAlignment.py,sha256=xBkl-2SGE6pHnf5OnjHqTfQeKYwpmskb3fqDTPU_3Dg,1342
nipype/interfaces/freesurfer/tests/test_auto_Concatenate.py,sha256=WMfo0_cx_cMmw0IiyhMwKS9de6LdanzPvEevYIWl4U8,2153
nipype/interfaces/freesurfer/tests/test_auto_ConcatenateLTA.py,sha256=l_dkTA9O_tlFqMcpozG4SyfA_qw_Hf5kbv1syY18lm0,2059
nipype/interfaces/freesurfer/tests/test_auto_Contrast.py,sha256=KxucSRlXtVgEf_Cz3JJGKEGH3nOaiMrDOwGaOb-mKQI,1767
nipype/interfaces/freesurfer/tests/test_auto_Curvature.py,sha256=DizhyOKYDYVk3HWOv3SZQrAaM3IakWRzuUXjCQeDHpo,1429
nipype/interfaces/freesurfer/tests/test_auto_CurvatureStats.py,sha256=zRLRQmHY4HqdZB-s60OTdTAInbba50LIIWYMK9U8W8s,1936
nipype/interfaces/freesurfer/tests/test_auto_DICOMConvert.py,sha256=iS8Z8CwwNMeh0W3P6VAbyBgsSkrZZeBqTe-wjgEcchU,1080
nipype/interfaces/freesurfer/tests/test_auto_EMRegister.py,sha256=0GY50fyXlCE9dmDwrdayyUFkSMUkBhGyY-u4SKy5tZI,1747
nipype/interfaces/freesurfer/tests/test_auto_EditWMwithAseg.py,sha256=_5VBYEjS4Nd0WXNWiuTccQ5IKIGOmcWz5jkz5IKmGk0,1517
nipype/interfaces/freesurfer/tests/test_auto_EulerNumber.py,sha256=nZWvh9GCo1iuC4SfGtIFKrB0e3kgdq5T8tEM38vzDCQ,991
nipype/interfaces/freesurfer/tests/test_auto_ExtractMainComponent.py,sha256=fhpCvs5-Xbim7FmklmD6YIeJYuJVzx9niJ51TDAl-ko,1210
nipype/interfaces/freesurfer/tests/test_auto_FSCommand.py,sha256=v4xHmJIXWhRXrmMCAXc8fySSWKCa557EyuwddRlQIsg,521
nipype/interfaces/freesurfer/tests/test_auto_FSCommandOpenMP.py,sha256=lFrGfyEp8lgnTcNiBzSHKnDJ69Y0KvAMh2AHp62uRwk,567
nipype/interfaces/freesurfer/tests/test_auto_FSScriptCommand.py,sha256=CthPFjUBUzh7j1Z72A4YFPDu4O45q5zqZV9sgAhAeCc,539
nipype/interfaces/freesurfer/tests/test_auto_FitMSParams.py,sha256=RVxBiE9emuXl-uTfavZu7V3jMMcVzqCME0V12DkOg1A,1323
nipype/interfaces/freesurfer/tests/test_auto_FixTopology.py,sha256=rsU5j5bP8Aa6dAV8fWRNyNtADHae0wnozkymfp6wlpM,1818
nipype/interfaces/freesurfer/tests/test_auto_FuseSegmentations.py,sha256=cBwHWkz_qQ8CJh7dD31Bip4fGXzf-0i_YhSaU5517rw,1512
nipype/interfaces/freesurfer/tests/test_auto_GLMFit.py,sha256=OQjRRsi2a9L_2QBqucsI0KrEFg50vBYT7C5nHUtZohM,6931
nipype/interfaces/freesurfer/tests/test_auto_GTMPVC.py,sha256=QHbpQEeUShP-KMn8Vmw9XvQ-30zFymJ0qnxjkIJqluQ,7061
nipype/interfaces/freesurfer/tests/test_auto_GTMSeg.py,sha256=Rf3UIYrv-AqNVpw3fDnzBBB19QUTyaeGXM4oZwJ4Twc,2154
nipype/interfaces/freesurfer/tests/test_auto_ImageInfo.py,sha256=0QZ5Fo6QbDyYGmd2WNC-xiPhh2YAvGZG8bIbzsozUvY,1209
nipype/interfaces/freesurfer/tests/test_auto_Jacobian.py,sha256=Je0EfJkbebOENe8_aNOShyn2uUWyizBxtT9XoRpv1hk,1402
nipype/interfaces/freesurfer/tests/test_auto_LTAConvert.py,sha256=3biXZWW-KGmY86PFscSD2iWbGq0ohxKq5z0hVwtswRo,3064
nipype/interfaces/freesurfer/tests/test_auto_Label2Annot.py,sha256=7tM5wFt8mztuTPVIEt_GO8v3ZOD888Uo0nBMl_J9QGM,1629
nipype/interfaces/freesurfer/tests/test_auto_Label2Label.py,sha256=8NfhtfidYcIkYOuosnJjS7JfidrWJEuTEMSxjHoq0Gs,2093
nipype/interfaces/freesurfer/tests/test_auto_Label2Vol.py,sha256=TlM1nQemXPlMDYxDUE9dsjM9m614L3cUVC-OISnwurY,3238
nipype/interfaces/freesurfer/tests/test_auto_Logan.py,sha256=ne2PVQ0yHMrHpUljKfX1QIRc6yh4PbIAMdquXx02eGQ,6958
nipype/interfaces/freesurfer/tests/test_auto_MNIBiasCorrection.py,sha256=ynR3Mk5NGXxQKX6fd2sGswJtu3DgFfqcqygUu8mKgI8,1888
nipype/interfaces/freesurfer/tests/test_auto_MPRtoMNI305.py,sha256=JDbPXLBzUzX2evB5cw7k2tFc6X_yHFJ_7CkleqKg0HU,1268
nipype/interfaces/freesurfer/tests/test_auto_MRIConvert.py,sha256=rVz6N5xPrG3LcGxJwGxycYgYUezhSgOrAfWC7OwOtF8,7346
nipype/interfaces/freesurfer/tests/test_auto_MRICoreg.py,sha256=ysK671_c81DvuHmfELqna2au2AlSEAK3Ylc1r0Fz2Zg,3608
nipype/interfaces/freesurfer/tests/test_auto_MRIFill.py,sha256=PAKvN0MMNL1odtBIKn9PeIU7jTJfET_c32qaLmpknKM,1485
nipype/interfaces/freesurfer/tests/test_auto_MRIMarchingCubes.py,sha256=e8ZS_E3iIBZ1yM2k4Mef2JgyO_JgqEOURV1S99P5YTM,1409
nipype/interfaces/freesurfer/tests/test_auto_MRIPretess.py,sha256=_KUVAQc3DtR7ox87Vd9gXjGUId1FVu64AtvwzP4RJLk,1694
nipype/interfaces/freesurfer/tests/test_auto_MRISPreproc.py,sha256=hFosnaeXUfjPGz07Ujtd4JA1UTdfVLrYAWBVBvsvM9w,2813
nipype/interfaces/freesurfer/tests/test_auto_MRISPreprocReconAll.py,sha256=6fC_wUflJ3rPyn-HL0HZgXtPq4yPPk8YSeOHLMCKb8s,3439
nipype/interfaces/freesurfer/tests/test_auto_MRITessellate.py,sha256=dKYHWOdvlxI5xjkN6HAdQJz1DfcSGd7-FqcffILvbM4,1417
nipype/interfaces/freesurfer/tests/test_auto_MRIsCALabel.py,sha256=mJs9o1D3jRqADXFPWSL7BgJ1QOIeZow62F1RFExDwiI,2235
nipype/interfaces/freesurfer/tests/test_auto_MRIsCalc.py,sha256=P8ajerfNo85uEMDT66-FvDsh4L-MWwrS6eG26RothF4,1624
nipype/interfaces/freesurfer/tests/test_auto_MRIsCombine.py,sha256=8UFWe0Y7R6M3_B6JoQJR20SsfWOtWLRUbfa4GaCu3mg,1161
nipype/interfaces/freesurfer/tests/test_auto_MRIsConvert.py,sha256=RfWdrsl3dBzCEbXwk2OcxxO8KGtiXN_Z6eEedVBJyG4,2606
nipype/interfaces/freesurfer/tests/test_auto_MRIsExpand.py,sha256=dmYinrDTEtDXSHp-Lfh_hYLK4lyDxFBzM2ShioHSNGQ,1929
nipype/interfaces/freesurfer/tests/test_auto_MRIsInflate.py,sha256=hDfn8jXoUw7Riw6veJgEmd-8KzzYvODFNRIVV9D8olE,1553
nipype/interfaces/freesurfer/tests/test_auto_MRTM1.py,sha256=5B3HAi-rEhcb7eZuwSzpH7kQx5SLguYR26w0MYjdg-M,6958
nipype/interfaces/freesurfer/tests/test_auto_MRTM2.py,sha256=gc5WrXsBCewU8QrM02eR2ih6R06c_udEeR59RjlsmRI,6958
nipype/interfaces/freesurfer/tests/test_auto_MS_LDA.py,sha256=FDBXYt5KFLvVQlDyQ4pvPYusSYzflYeq9mHSFMQ_sT0,1805
nipype/interfaces/freesurfer/tests/test_auto_MakeAverageSubject.py,sha256=j67JXfpMfDJrRxjrfX3BKtDwUBi5pwKIrCmibbQEtAQ,1123
nipype/interfaces/freesurfer/tests/test_auto_MakeSurfaces.py,sha256=ShwIb7Q3mY-7jYxGComKQYPoz7KWQLJ1ICrUcTawngw,2898
nipype/interfaces/freesurfer/tests/test_auto_Normalize.py,sha256=RnA0cMjg81-UcMyzzHdqc0Kwr5gVXpRS5qcnP9szsPA,1567
nipype/interfaces/freesurfer/tests/test_auto_OneSampleTTest.py,sha256=zFVcoit4ZN-LAHznUoi2PgxVamNQZ6FCZS2iAkwo5eo,6971
nipype/interfaces/freesurfer/tests/test_auto_Paint.py,sha256=ne1V27xsYdKfs9OsjAyeGQfxKmJiQOibx8LHPRWF6x4,1474
nipype/interfaces/freesurfer/tests/test_auto_ParcellationStats.py,sha256=N52UnMblRjRr9EXyn29IJCwSBeyBzHiCGqyCknGW8Qw,3182
nipype/interfaces/freesurfer/tests/test_auto_ParseDICOMDir.py,sha256=4ioKczpXfOAZRIrBB1cQs9xg6NZLrUC6GanJ4PWzo1g,1248
nipype/interfaces/freesurfer/tests/test_auto_ReconAll.py,sha256=D92ohWLmHAe_mf-nTkxOQy9LoeDpZLlkZbd_KhVBdBE,8261
nipype/interfaces/freesurfer/tests/test_auto_Register.py,sha256=gL3RD-OtZ5jte0YiysQOdEAKRzHHpgWKVQNyyOyoawk,1611
nipype/interfaces/freesurfer/tests/test_auto_RegisterAVItoTalairach.py,sha256=RrOaGQEmRZ-6q3EBEPwYZCBfBJHzAgf_g6c_iCGq0GI,1579
nipype/interfaces/freesurfer/tests/test_auto_RelabelHypointensities.py,sha256=yHdMOrvOh4YCdsC7qAxu0MNA2GmdakJtw7y7rcp3Ygk,1693
nipype/interfaces/freesurfer/tests/test_auto_RemoveIntersection.py,sha256=hyHbCjtE-0-z1zyA9Pqen7S9e55sewWrp_WSLDy4sWo,1315
nipype/interfaces/freesurfer/tests/test_auto_RemoveNeck.py,sha256=Auz08T45zMwsruygB7Stud3peJe0R7XJH2N68jEJpcA,1603
nipype/interfaces/freesurfer/tests/test_auto_Resample.py,sha256=6WEwVJMcseZqjrC81vGZN_U9ek23VVVe7T36I-XLt4M,1260
nipype/interfaces/freesurfer/tests/test_auto_RobustRegister.py,sha256=ZlRJd7fMRVMQorepWVR7Z5b4Q3GgbCf2UqAXjtm2sKU,3803
nipype/interfaces/freesurfer/tests/test_auto_RobustTemplate.py,sha256=gA0f5GMEt14P-95GV3juMQSLu6PA4xjb0HBdYWX8-0A,2308
nipype/interfaces/freesurfer/tests/test_auto_SampleToSurface.py,sha256=-wzS2PXg3aOTN240hfljL1INS7CsBpkny0ZYtI1vApQ,4279
nipype/interfaces/freesurfer/tests/test_auto_SegStats.py,sha256=2iVZsWONHy5lxfR8HjCjFpdBrfZBNMuDa6sTb3qrMD0,4682
nipype/interfaces/freesurfer/tests/test_auto_SegStatsReconAll.py,sha256=rc-3GHf6URu3eI-jhICMAq0_s_cE6TjeEzvylER5KxU,5738
nipype/interfaces/freesurfer/tests/test_auto_SegmentCC.py,sha256=15KUEr-LCOl6x28au1v9p47j5Gi7KbqdxD1NoVjoYT4,1670
nipype/interfaces/freesurfer/tests/test_auto_SegmentWM.py,sha256=W-W8QIRKK2LpQqsFPpAmwZWIJSibMGA4Ubd6IhoaRYA,1144
nipype/interfaces/freesurfer/tests/test_auto_Smooth.py,sha256=4KvCiaNL2Xa7IadAS5chGTaE4d1ofONxfpix0yl83-E,1810
nipype/interfaces/freesurfer/tests/test_auto_SmoothTessellation.py,sha256=tzlrn-vdcA9ZfYgKdFjVd6I-LJmvlBcHCYA7tdWFTzE,2154
nipype/interfaces/freesurfer/tests/test_auto_Sphere.py,sha256=H8VBNpkZPUQYbhaYZFIW3AFn-yG7FzwwKIjdbK4pfEI,1467
nipype/interfaces/freesurfer/tests/test_auto_SphericalAverage.py,sha256=LGHb2oBqXf83djFFWxFSD2mhS21Sr9x15J-ieA7bVmA,1929
nipype/interfaces/freesurfer/tests/test_auto_Surface2VolTransform.py,sha256=xuaEUDJ53mArM6GW6Gx5CH7JBZJDs_YBikR5aZ7HLrQ,2341
nipype/interfaces/freesurfer/tests/test_auto_SurfaceSmooth.py,sha256=FAXuQyyS0blQ6-LfAYhh75W_xqxas2OE1Jzy64Zfv0Q,1657
nipype/interfaces/freesurfer/tests/test_auto_SurfaceSnapshots.py,sha256=qKqWvj-x7I7_Xs3FQGpN0n5kOFHTu-M898qRM53JlbI,3794
nipype/interfaces/freesurfer/tests/test_auto_SurfaceTransform.py,sha256=sbufNgQlrTsse5xdd1Vi62JiGGAoi_2QtcTn6gyDbR4,2053
nipype/interfaces/freesurfer/tests/test_auto_SynthesizeFLASH.py,sha256=zvbTGLLvaYMC-POZe2OJxym1xPhS_0VmeYdjgU8nGzY,1706
nipype/interfaces/freesurfer/tests/test_auto_TalairachAVI.py,sha256=gXtSG9Cc6agA3fs8X7S5g_mZRgZnU81CMGfllXRsW2Q,1302
nipype/interfaces/freesurfer/tests/test_auto_TalairachQC.py,sha256=ioQJbgx7ZL5NCFS_EL3ssfSYpvBjAEdNZlfYLRGg_qg,1037
nipype/interfaces/freesurfer/tests/test_auto_Tkregister2.py,sha256=QTDJw_F6e2zQm3PSlzn7CJI3Jk9aC6LsSwpTLMs9Zi8,2512
nipype/interfaces/freesurfer/tests/test_auto_UnpackSDICOMDir.py,sha256=vS9Mw9mjuZ7PTFLrOAw0LGd051dlXYO8ZmB1dnigyB4,1693
nipype/interfaces/freesurfer/tests/test_auto_VolumeMask.py,sha256=-5949vA1mFqKoMWSt1oH-Lpa-b0EIh8XYfUmfF3pmMQ,2272
nipype/interfaces/freesurfer/tests/test_auto_WatershedSkullStrip.py,sha256=Z7wP3wM4b4zh-GC4NclxAbcXTPDXDCD_rr0M6pdXq3A,1520
nipype/interfaces/freesurfer/tests/test_model.py,sha256=D7APlwvQknFMmVa7qvIBbDRxrhcXAWzlaQUgswp8xOw,1896
nipype/interfaces/freesurfer/tests/test_preprocess.py,sha256=eLpx19a6gC_ofZLbg2rTyH9ZMaGqMXS3kMxnl_28_-Q,6202
nipype/interfaces/freesurfer/tests/test_utils.py,sha256=L10UZCFM3sh0k-GM6Kh1TCKQb3JPEp9_Fnea3zF-5pw,7378
nipype/interfaces/freesurfer/utils.py,sha256=A_a__nvVmSawXQYUrhu0gbPlOJPeoKOZFLO0hn8JEtw,138396
nipype/interfaces/fsl/__init__.py,sha256=r98f-NgWMGJ3SknkiwyLKbrIxJoJSOhqtxUVzwgGQPs,2220
nipype/interfaces/fsl/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/aroma.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/dti.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/epi.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/fix.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/maths.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/model.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/possum.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/fsl/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/fsl/aroma.py,sha256=xyPRzvpf3ElRD2ZLVPLizASGcQS6o5vynijb7xhHHtw,5628
nipype/interfaces/fsl/base.py,sha256=8K5avnADqV0omvJYwGHBVbfnM4BC0UDORw46PJZ0M1c,8013
nipype/interfaces/fsl/dti.py,sha256=2NOXLzpHVcqZ5_ooi-ymJIPmYr6fT8JJlEnvUtdxCDk,52529
nipype/interfaces/fsl/epi.py,sha256=H1zrfwk6uDxMAr3bD0-zQNdbpRvcR8h2F3kfRPzz6Ik,57834
nipype/interfaces/fsl/fix.py,sha256=gTWmLD5wUC8WtSr7FHxY4Kcm4pNR8on9aOfjiZrQFMs,12069
nipype/interfaces/fsl/maths.py,sha256=7AlxLBQM6PZycXtTIQ3pXO8GlZngJGf6_7yauvFoPv0,14678
nipype/interfaces/fsl/model.py,sha256=F8ThDFYaEIsB1N-e9N8cdJPJwVNCh1PmAYUEvqAuvYY,90319
nipype/interfaces/fsl/model_templates/feat_contrast_element.tcl,sha256=Rv4opcBB3XNjp21dIZDM64WJeeS4vgNrX2yb9bLhmbU,93
nipype/interfaces/fsl/model_templates/feat_contrast_ftest_element.tcl,sha256=8gpgTRIAOFsR3hfZSg4YDXX53YvNtUoL1id5VL_4fTc,74
nipype/interfaces/fsl/model_templates/feat_contrast_header.tcl,sha256=CjkEXxR7-D6lvb3PEeOAwDHU2EIrj1nQvsfwn99rJcw,134
nipype/interfaces/fsl/model_templates/feat_contrast_prolog.tcl,sha256=kBcHswL0T2YDgZGjEF5qsFdQGMF79yn82GOi-5wMNV4,150
nipype/interfaces/fsl/model_templates/feat_contrastmask_element.tcl,sha256=8CazSgRVrKVDXWGqkFcT0ClMZp5SzO7lsWJ4cNByOCs,95
nipype/interfaces/fsl/model_templates/feat_contrastmask_footer.tcl,sha256=ehIcyyj-EpOQ1HxEZ0JfIUH3-4a3F2vxotqJ-_-NUFw,53
nipype/interfaces/fsl/model_templates/feat_contrastmask_header.tcl,sha256=mVXaXesQZg_8ObNuVBMn3Mp8qmqXWayRxWs2O0nkjkY,87
nipype/interfaces/fsl/model_templates/feat_contrasts.tcl,sha256=tyhHv9SAxygi2T398D8HxslcIUoFAdp6W_9gEBYWu00,16349
nipype/interfaces/fsl/model_templates/feat_ev_custom.tcl,sha256=tRKTfSgj67ASI8QvqZd74zhyy-jj3v0Yg1gRdYlAsdo,1013
nipype/interfaces/fsl/model_templates/feat_ev_gamma.tcl,sha256=LXTps8IVoDMY793BfuGiEoAtPs_qMbQfjOia33QWGOA,798
nipype/interfaces/fsl/model_templates/feat_ev_hrf.tcl,sha256=FFOTbEubG1mt8HPPwgd0k5a-Gp_rUU-3q9RoUlUeyZM,777
nipype/interfaces/fsl/model_templates/feat_ev_none.tcl,sha256=bo-Zu5rqJSKCNbCyEDEeN9ijG5B55S05ybCLRiEDxG8,634
nipype/interfaces/fsl/model_templates/feat_ev_ortho.tcl,sha256=XqDMjiKbDf6X9d6zr04btcNceqlKbPlimBrzkiuaP2E,69
nipype/interfaces/fsl/model_templates/feat_fe_copes.tcl,sha256=KsXXQ6XgNz7Rd1mZbE5X7dzl7yS05cWOZhTl1MqHZak,90
nipype/interfaces/fsl/model_templates/feat_fe_ev_element.tcl,sha256=3OM4oslQEUTd3pp-v3emPuB9iCYSrXx4o4qpgHQKCKI,143
nipype/interfaces/fsl/model_templates/feat_fe_ev_header.tcl,sha256=w0yLjRCqNMkDD0ZyeeU8pUEn_0Upyl7qTY5w85jzwe0,788
nipype/interfaces/fsl/model_templates/feat_fe_featdirs.tcl,sha256=Xblnk3RNpkEpFYvtGnmXd5mYqwQ2bVNZ8zR16X-13Ic,77
nipype/interfaces/fsl/model_templates/feat_fe_footer.tcl,sha256=xtCPFTw4SqFZh0EvjzUE1f9B2bLHDma9e7KYbEB9peg,1117
nipype/interfaces/fsl/model_templates/feat_fe_header.tcl,sha256=Qdlk8-x5xhHJC5qVqXwU5ivHfzPOlPzaGH3zECdl7z0,5289
nipype/interfaces/fsl/model_templates/feat_header.tcl,sha256=FsMiBnJtLq2m5qHF9znA5iO6va4HdSUWav2U7SVYfTU,5394
nipype/interfaces/fsl/model_templates/feat_header_l1.tcl,sha256=z4rVfJzAPVZAExNTscw-7ugg40u1Fg7hly7Fe3g6a4w,5410
nipype/interfaces/fsl/model_templates/feat_nongui.tcl,sha256=D5hHAv5W7Fc6eYCRE8FZ0u09s6g04gvdplsA0KSORcU,644
nipype/interfaces/fsl/model_templates/featreg_header.tcl,sha256=vMPkr_VIC3_OaU47voOSE1gVO0gbJbP38Xz5kidgmoc,5276
nipype/interfaces/fsl/possum.py,sha256=fmkzVy4b2HkobpROWek_s0BRRRfSSKrqAcGr5bpAXzU,4129
nipype/interfaces/fsl/preprocess.py,sha256=svDI-w5KMVRu-K0hzbET4iEkj-KVlExk_0WZP0ohZns,76864
nipype/interfaces/fsl/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/fsl/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_FILMGLS.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_Level1Design_functions.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_AR1Image.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_AccuracyTester.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ApplyMask.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ApplyTOPUP.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ApplyWarp.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ApplyXFM.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_AvScale.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_B0Calc.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_BEDPOSTX5.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_BET.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_BinaryMaths.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ChangeDataType.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Classifier.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Cleaner.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Cluster.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Complex.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ContrastMgr.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ConvertWarp.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ConvertXFM.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_CopyGeom.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_DTIFit.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_DilateImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_DistanceMap.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_DualRegression.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_EPIDeWarp.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Eddy.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_EddyCorrect.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_EddyQuad.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_EpiReg.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ErodeImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ExtractROI.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FAST.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FEAT.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FEATModel.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FEATRegister.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FIRST.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FLAMEO.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FLIRT.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FNIRT.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FSLCommand.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FSLXCommand.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FUGUE.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FeatureExtractor.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FilterRegressor.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_FindTheBiggest.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_GLM.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ICA_AROMA.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ImageMaths.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ImageMeants.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ImageStats.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_InvWarp.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_IsotropicSmooth.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_L2Model.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Level1Design.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MCFLIRT.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MELODIC.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MakeDyadicVectors.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MathsCommand.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MaxImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MaxnImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MeanImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MedianImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Merge.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MinImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MotionOutliers.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MultiImageMaths.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_MultipleRegressDesign.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Overlay.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PRELUDE.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PercentileImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PlotMotionParams.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PlotTimeSeries.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PowerSpectrum.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_PrepareFieldmap.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ProbTrackX.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ProbTrackX2.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_ProjThresh.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Randomise.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Reorient2Std.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_RobustFOV.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SMM.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SUSAN.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SigLoss.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Slice.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SliceTimer.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Slicer.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Smooth.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SmoothEstimate.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SpatialFilter.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Split.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_StdImage.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_SwapDimensions.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_TOPUP.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_TemporalFilter.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Text2Vest.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Threshold.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_TractSkeleton.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Training.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_TrainingSetCreator.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_UnaryMaths.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_VecReg.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_Vest2Text.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_WarpPoints.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_WarpPointsFromStd.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_WarpPointsToStd.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_WarpUtils.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_auto_XFibres5.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_dti.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_epi.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_maths.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_model.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_preprocess.cpython-311.pyc,,
nipype/interfaces/fsl/tests/__pycache__/test_utils.cpython-311.pyc,,
nipype/interfaces/fsl/tests/test_FILMGLS.py,sha256=pgYLKyrZAWe2FeNbygY_7eWkH-qzJNUaPPeTpxyJXYA,4910
nipype/interfaces/fsl/tests/test_Level1Design_functions.py,sha256=6VERN9Rg2ypD5terPPIUhXvWtqKidcdCb8CilOQFl58,1107
nipype/interfaces/fsl/tests/test_auto_AR1Image.py,sha256=hDo3Cf6zNNhJVmxyOwpqd9JuBwSnPjYl7xWIVKiGK_I,1555
nipype/interfaces/fsl/tests/test_auto_AccuracyTester.py,sha256=QM5OQaCPL7fuFaXxsxoOpvo3Nlyv08lerFvP2FDX5Nc,1286
nipype/interfaces/fsl/tests/test_auto_ApplyMask.py,sha256=A-gJfpSkSFQOUQgu5DLq8vgh9xImlvldJA6z9FhLPPQ,1589
nipype/interfaces/fsl/tests/test_auto_ApplyTOPUP.py,sha256=WCz44NL8o_JODYLlec1nmw1tyFyzNdAJXoFJ00DaVNs,1893
nipype/interfaces/fsl/tests/test_auto_ApplyWarp.py,sha256=7joR_4wNr8fRZCl5Uw_iH5-NblQQfJFXE0BQ1sBdNGI,2218
nipype/interfaces/fsl/tests/test_auto_ApplyXFM.py,sha256=rGscxZkFu-h_X3LlgfF5HINTei_hQdrCoroJT4e1k2s,5667
nipype/interfaces/fsl/tests/test_auto_AvScale.py,sha256=IW9Q0NstE5wR8f6yZaJNbpZh46TpbFEIrVuJzFU7qLI,1384
nipype/interfaces/fsl/tests/test_auto_B0Calc.py,sha256=sbaMgTzSX8E859AHF5AagqR4Dxjhlv7e7hbxN8uz7wU,2478
nipype/interfaces/fsl/tests/test_auto_BEDPOSTX5.py,sha256=2k7vePU0lCzw3bQnjad5LviI94bvX6rr1uHzkNl1SXk,3637
nipype/interfaces/fsl/tests/test_auto_BET.py,sha256=AWQFFxoJulC8gssaeY4Kf3RCd4FnFARKjzAbtLWPYPE,4676
nipype/interfaces/fsl/tests/test_auto_BinaryMaths.py,sha256=8T4ArDwnPtRcx4TByZQeuhSxggW8Vf7zj9tPQLXMqdk,1897
nipype/interfaces/fsl/tests/test_auto_ChangeDataType.py,sha256=SNS8X_Ec_SMECln-seIaAUEISsCaZ0Bii7ctYexBwiU,1496
nipype/interfaces/fsl/tests/test_auto_Classifier.py,sha256=lJAK0ndXDVUF-2K0f-ZxW-2Ym4amR-eIap8CYiQQIHA,1313
nipype/interfaces/fsl/tests/test_auto_Cleaner.py,sha256=JQP_ZirDs5XvNcvfusGreD6naHbB5NoI98955remzCQ,1639
nipype/interfaces/fsl/tests/test_auto_Cluster.py,sha256=DwmrqJ-F09mD1893a8IlS_mwv2UUTQWhSQNxYAB8EO0,3725
nipype/interfaces/fsl/tests/test_auto_Complex.py,sha256=xI9QvFcBh_JtbnJ0RhvC15g6pfYChsH9tofTQ1NdNRQ,6307
nipype/interfaces/fsl/tests/test_auto_ContrastMgr.py,sha256=JTv3C8n9mfX411UOev7mwD2abJRCJswzMjFNtRT9YF0,1887
nipype/interfaces/fsl/tests/test_auto_ConvertWarp.py,sha256=s2Dz3ezkhk9s9f681IQ0YHuBzJf76uil0RIqFTsasiM,2541
nipype/interfaces/fsl/tests/test_auto_ConvertXFM.py,sha256=VSkH2LYpod9ENvxkO-JeibJIOJw6z5uw_8fPbAHg1xA,1835
nipype/interfaces/fsl/tests/test_auto_CopyGeom.py,sha256=ycE7Dt-2P7wxHzr3y1PIbNHrhe-3_anIbTOoFGg2Umo,1353
nipype/interfaces/fsl/tests/test_auto_DTIFit.py,sha256=dOEDQvNlBg9VTfzeTxfTauetX4rkDWEPR2gdDnbJ804,2885
nipype/interfaces/fsl/tests/test_auto_DilateImage.py,sha256=rRgJr3qVR4IQStKtALLnzw9heFEL2-ve9MAOi5CUwew,1933
nipype/interfaces/fsl/tests/test_auto_DistanceMap.py,sha256=V8UwaAa1rltd4cFsH7HuAELTfAIYWWvX_T9h5vj6Dt8,1483
nipype/interfaces/fsl/tests/test_auto_DualRegression.py,sha256=0iTBssjETOYRBPRHr_9EPJLrbFXtFfif9Qjv5zgW71c,1805
nipype/interfaces/fsl/tests/test_auto_EPIDeWarp.py,sha256=Sv9G9mk47b5oHBsSeuaITYztSDy2S28zmLytxoYiIuk,2301
nipype/interfaces/fsl/tests/test_auto_Eddy.py,sha256=bdF112g0RfBnLh8A0HU9DZIiMHBQxypMYW-fJu8F8nA,7157
nipype/interfaces/fsl/tests/test_auto_EddyCorrect.py,sha256=zElwfcrYoIlWhoiWpgPi3QG6oGeXiESnd4VcwEEOAnQ,1376
nipype/interfaces/fsl/tests/test_auto_EddyQuad.py,sha256=JOSWYLQkUAMrA5d4P-MO7jghZzIpwt5tcUlaGJXTDDc,2305
nipype/interfaces/fsl/tests/test_auto_EpiReg.py,sha256=kRiC0Kv62C_U0IPKkq8iDLNZiNnQVvIDxzhGsYluQ7Q,3017
nipype/interfaces/fsl/tests/test_auto_ErodeImage.py,sha256=QebjtK03V2gnQI5wjtxhNgSIJP-qPEduBX4sWRUDkEI,1930
nipype/interfaces/fsl/tests/test_auto_ExtractROI.py,sha256=9s6JrIlwxWRL8CPjIlzxlqW0RnjJlkL2rOGfZfa0AZg,2134
nipype/interfaces/fsl/tests/test_auto_FAST.py,sha256=wkMSKLhIrjPNdY4GntaB-pDJeWx-UbdFID9MAZZ-WRM,2759
nipype/interfaces/fsl/tests/test_auto_FEAT.py,sha256=JZ8xsSpkJMtw5jTrnLwS8eceSxKEZ--rBC6ASwc0foo,934
nipype/interfaces/fsl/tests/test_auto_FEATModel.py,sha256=gDhm9iGHiraYjkk70qEfP0NVIO1QHHtptA9MQ7qeFXk,1426
nipype/interfaces/fsl/tests/test_auto_FEATRegister.py,sha256=G6Ajp4nUBGOQoEIyZve7Nz8BALjYGxvgJoqOy1GR5bs,919
nipype/interfaces/fsl/tests/test_auto_FIRST.py,sha256=bZpHtxGpo4XelcPBdDzkObtn4vz0Wfs09mNQty2vl34,2127
nipype/interfaces/fsl/tests/test_auto_FLAMEO.py,sha256=0xqdKL4SFXvMUce43jnihsxsco3Rja2FavdJWrvBSTs,2807
nipype/interfaces/fsl/tests/test_auto_FLIRT.py,sha256=p83wtN1-Z6x1k_Wxt2ieUyx1ybBsoOJYXORBzAbu5Ao,5623
nipype/interfaces/fsl/tests/test_auto_FNIRT.py,sha256=OxUM9ilEmfNJukeWZplM7ZM97A8VsTz1LEsutfI3H5I,5278
nipype/interfaces/fsl/tests/test_auto_FSLCommand.py,sha256=APU_JAMdxaMPTkOZLbfYwC6efT-CZWqq9oRucf1hR5I,523
nipype/interfaces/fsl/tests/test_auto_FSLXCommand.py,sha256=cM0mXN8Los3GMkGO3LXzwq1FhFC_BnOQ7dvdyDQB5_Q,3509
nipype/interfaces/fsl/tests/test_auto_FUGUE.py,sha256=6-5JMxIEuV-FAmJTWx4TzXCgvuRz6Ui10Cku4yCEiZ8,3920
nipype/interfaces/fsl/tests/test_auto_FeatureExtractor.py,sha256=tVG5Z8Nouc7-rUzQ5s4veuLYLLu_OCkRtZxb08K_nxA,1021
nipype/interfaces/fsl/tests/test_auto_FilterRegressor.py,sha256=WZeLm3phitAb5iU_lyhL4jd5d9avachuvVjh-IuEwhY,1877
nipype/interfaces/fsl/tests/test_auto_FindTheBiggest.py,sha256=wRWebk9AWMERigKSJTaeiYbtNeLdhLrxdwC2lwnXWqM,1184
nipype/interfaces/fsl/tests/test_auto_GLM.py,sha256=VK-dkXFIHFvVu2dYhKgZuvUSa4PoMaR3VDiZzN9yyTc,3213
nipype/interfaces/fsl/tests/test_auto_ICA_AROMA.py,sha256=v5kG_UJr4R95fDVDWKU0ckcFrY8-9vspQ6CEJmpVj1U,2225
nipype/interfaces/fsl/tests/test_auto_ImageMaths.py,sha256=-huun1BWAstwFbxqz7UxakulzK1ylixlNxqNtdHGpWU,1577
nipype/interfaces/fsl/tests/test_auto_ImageMeants.py,sha256=gcaNY579nDzLDqQfsbpE9Cg-6GOCMGqHciKyxCreUjE,1719
nipype/interfaces/fsl/tests/test_auto_ImageStats.py,sha256=Qlex9lRyARUHe_ZeyNBIEnQ2VDj5wchvdkRCD3qLpGU,1367
nipype/interfaces/fsl/tests/test_auto_InvWarp.py,sha256=dEJkFL1zBfSQrwHhyIgdbG1c-kvJ80nUtcNv6ctWfO8,1832
nipype/interfaces/fsl/tests/test_auto_IsotropicSmooth.py,sha256=vmy_mot6oHg--QCBj3KUMhDQIcJgNYckFhx1nXyXNqw,1751
nipype/interfaces/fsl/tests/test_auto_L2Model.py,sha256=Fy4N3PjnMItDdXbDYit7MTC2YQA6Ssi_8W71H-OfXN4,872
nipype/interfaces/fsl/tests/test_auto_Level1Design.py,sha256=vvouXnOAkaQw7PJhDEnHV8iY--V7dJxLwDpIDp9z2xw,1064
nipype/interfaces/fsl/tests/test_auto_MCFLIRT.py,sha256=89vswyO3GoN4ymWIcp4o9wq1oNxUn1Oqb8UDbFm93ik,2685
nipype/interfaces/fsl/tests/test_auto_MELODIC.py,sha256=ZIpNcB10cYP83lFxDoYgK5oXv9TMqF-2UbZg3PQKQ4M,4332
nipype/interfaces/fsl/tests/test_auto_MakeDyadicVectors.py,sha256=tXRjxBKAqvZgY5W-QbBiUaddmS2SzkjCWefternr95E,1593
nipype/interfaces/fsl/tests/test_auto_MathsCommand.py,sha256=yxGAsDL2UTyMa5tgPV8OGhwBbcaBMnFUQj7w_uF3GQs,1458
nipype/interfaces/fsl/tests/test_auto_MaxImage.py,sha256=_GwZy6_Q3fv7tiugxFj5OsoTFMaljpU6MJh_KEknT-c,1555
nipype/interfaces/fsl/tests/test_auto_MaxnImage.py,sha256=FAQbagQSKH6UKbPLnCo4sGeJkHtzJkzgXUe7MzA-_JY,1561
nipype/interfaces/fsl/tests/test_auto_MeanImage.py,sha256=TB-WHiTODOOyYD_ObEOl8NAvool_6fO56668-3aXIeo,1561
nipype/interfaces/fsl/tests/test_auto_MedianImage.py,sha256=_z5EqXlLOP79w-GNqe4CbXNGRzIhjRxnImNsUxFVjWs,1573
nipype/interfaces/fsl/tests/test_auto_Merge.py,sha256=vs7T53cO6R-UNMLlSXJOj3hYScFvkI-wmVrZ88H-SAA,1364
nipype/interfaces/fsl/tests/test_auto_MinImage.py,sha256=uSdJs7bMg5KXTVE-QhAN6Lsr6hMupJqN85ncrQAEYZM,1555
nipype/interfaces/fsl/tests/test_auto_MotionOutliers.py,sha256=kzFWcSVUzefB3YIyxwHsds8jiC0NYm4QNV4_ghSeVXI,2214
nipype/interfaces/fsl/tests/test_auto_MultiImageMaths.py,sha256=h55MSQ5aJf35F1kcGrUqKdCMVj7i6AKdohFK2D4eaO8,1652
nipype/interfaces/fsl/tests/test_auto_MultipleRegressDesign.py,sha256=uuD34AE8zwsWMouewVJFt9SpK6m9CRCwHYLf2FdmOik,1094
nipype/interfaces/fsl/tests/test_auto_Overlay.py,sha256=X3bww_Wd-2-B8PnPew7Up-mCokJmEy9Xfo3ftt4PJ4M,2674
nipype/interfaces/fsl/tests/test_auto_PRELUDE.py,sha256=8jv7G1U_hCHLGPIkwGU7O3Jqd-K8c5S18Pmzh_Ls3Qg,2686
nipype/interfaces/fsl/tests/test_auto_PercentileImage.py,sha256=nNlXLfnxcv4ognAChITdayrqv6rf6n6ASillgy99pkg,1670
nipype/interfaces/fsl/tests/test_auto_PlotMotionParams.py,sha256=x9pWR3Uk3dzP9rCzc2l5SivRQAS3SNyrYDM77fnO_8I,1360
nipype/interfaces/fsl/tests/test_auto_PlotTimeSeries.py,sha256=KSyz2qok2ZGC6d6J6EGpYtIE76uFW4Ujnjp55WD7SOU,2240
nipype/interfaces/fsl/tests/test_auto_PowerSpectrum.py,sha256=OAibUU0ARqKbqfrOgAzef786T4-wZCWi7GEJmlXyCN4,1184
nipype/interfaces/fsl/tests/test_auto_PrepareFieldmap.py,sha256=i8gTTT_Qv4MTg4Wa_8-v9GWIgJdwmt5NrQGK807S_jk,1660
nipype/interfaces/fsl/tests/test_auto_ProbTrackX.py,sha256=eQo6Z7BD612_ZS_sWseBfeFhKdEHRxgsWeLHfsNBafw,3919
nipype/interfaces/fsl/tests/test_auto_ProbTrackX2.py,sha256=hjVUwdsUxHJNPBgN8pRL8J4hLiBJz-9XnPcQU0Lqr6c,5429
nipype/interfaces/fsl/tests/test_auto_ProjThresh.py,sha256=-hIPmv182B9Iv8drEly-2IvwR4iphi9Wa7oMzl-F1xE,1046
nipype/interfaces/fsl/tests/test_auto_Randomise.py,sha256=p2m7TRMiDHLcT89V-Vv5D91CU_WT_l3synLP3zoBfb0,3030
nipype/interfaces/fsl/tests/test_auto_Reorient2Std.py,sha256=fnH1Z-bfZwnILHA3o4DOcRhflc8g4h4ugdhYw3utE_k,1131
nipype/interfaces/fsl/tests/test_auto_RobustFOV.py,sha256=2GJ0N03btNIhPF_d3c4YSNCIg00QvwM8CHgP0SW4Bik,1524
nipype/interfaces/fsl/tests/test_auto_SMM.py,sha256=Yus_owJ8zKnkbBPtQ4Iyb7YTLViCcnFRSh8Q4blqH0o,1437
nipype/interfaces/fsl/tests/test_auto_SUSAN.py,sha256=t4k-UpYIf_4BfEvWka1d1aPZlDU-yYO4UXM5RLhl-L4,1725
nipype/interfaces/fsl/tests/test_auto_SigLoss.py,sha256=s82srOp_gLSALhLYNlKKBO66kfWmMBqNG4zGEtqI6Wc,1308
nipype/interfaces/fsl/tests/test_auto_Slice.py,sha256=11Cd2ygnY6sAnfpMl_8Paifk7wKqLbodDab1hZJcX9o,1055
nipype/interfaces/fsl/tests/test_auto_SliceTimer.py,sha256=CN_WE-ZzAAXcYer8L5R8EXW7HES9-23G4Kp_YTlFzIw,1736
nipype/interfaces/fsl/tests/test_auto_Slicer.py,sha256=jqPIgBJbqyR7mI3ijx65WDMAhDGR6SjvrGc9M0UW5UQ,3042
nipype/interfaces/fsl/tests/test_auto_Smooth.py,sha256=Gp61Jr9cilmgjH2DZ3z5mriHtL33K9g7hvRyyThXcmE,1525
nipype/interfaces/fsl/tests/test_auto_SmoothEstimate.py,sha256=5T0Aqy-Q21T_Q-I_jMM-sOf3arLNAR4P5Rrb5ljucWk,1385
nipype/interfaces/fsl/tests/test_auto_SpatialFilter.py,sha256=dORTXz-ugAcBHP19xyksJwnY2fioUUFrMp2K_IZuRHA,1941
nipype/interfaces/fsl/tests/test_auto_Split.py,sha256=5vuuwIdRA80t04PUxoB5diNKkQgpFqFGm1v5l9EI_Gw,1140
nipype/interfaces/fsl/tests/test_auto_StdImage.py,sha256=mvTINry0Ge2ywNkeGad1H-IGbla_T9jnfhEY2p7lpmE,1555
nipype/interfaces/fsl/tests/test_auto_SwapDimensions.py,sha256=jkTo8z-nv34p2J1VIya2jGHYbI1vFyqZCCp1QqVJJhg,1260
nipype/interfaces/fsl/tests/test_auto_TOPUP.py,sha256=Cmab-wnCDhoK2x2vX_O6GehheNE-6ETTPq5xRs2JNQk,4159
nipype/interfaces/fsl/tests/test_auto_TemporalFilter.py,sha256=-YEoONVmhkXXs6aHhO9W8Rs1fnWgdYPM-uA-JGCmoy4,1713
nipype/interfaces/fsl/tests/test_auto_Text2Vest.py,sha256=nJET0cBcGztdkyvIexlVeHSHc0OKt-s81tnhqd0dDlA,1136
nipype/interfaces/fsl/tests/test_auto_Threshold.py,sha256=NMbbeVX0cyr9j8ze5bb8gIpQXcKdE2Q5bjG_l0Oro0k,1736
nipype/interfaces/fsl/tests/test_auto_TractSkeleton.py,sha256=O1o65IC9Al4-FhspdLWWIorNpSQFBzvH7nuL1esmvhg,1916
nipype/interfaces/fsl/tests/test_auto_Training.py,sha256=nRu6d2ugz6cOsT6uTGdhvS3rUnFroIFrmfrJMVjIxq4,1115
nipype/interfaces/fsl/tests/test_auto_TrainingSetCreator.py,sha256=tAUF3HG1yKNW2G0L-EHmDkcmq2MYH8PFULvUxe_mf8E,898
nipype/interfaces/fsl/tests/test_auto_UnaryMaths.py,sha256=R0qZUR1NHnMFKrIj7q1SS9rZRDZMcYmepA8X0D11btQ,1561
nipype/interfaces/fsl/tests/test_auto_VecReg.py,sha256=3q38BG6oRDgKeOKmbENixM1m4vnbxy4_K7aMzRkzDXA,1871
nipype/interfaces/fsl/tests/test_auto_Vest2Text.py,sha256=HY408OZV0Vw1yoW_1_UI9DOk8hH6n5Z7XKNP1HIeCI4,1137
nipype/interfaces/fsl/tests/test_auto_WarpPoints.py,sha256=w-Om2dzm40vvfA00jsyAgQTHPXvCzs6hKvoHxN0zck8,1827
nipype/interfaces/fsl/tests/test_auto_WarpPointsFromStd.py,sha256=D3t1tioCpldcO-ZC_04wYbSsoiHO7_RqOurOq3tlmZA,1685
nipype/interfaces/fsl/tests/test_auto_WarpPointsToStd.py,sha256=KtLFRmadynS7k6rbXRD5vAN0E3zT1YtekjuRCJwtYuQ,1949
nipype/interfaces/fsl/tests/test_auto_WarpUtils.py,sha256=aSspB3eQ1yxSSLl--PtChYV672PsCppOJw2PVvEcRNw,1866
nipype/interfaces/fsl/tests/test_auto_XFibres5.py,sha256=1i9SxQVVDd1B7gGLXbZu_y1qpGMRZp-1AVhOT0_wKZ0,3597
nipype/interfaces/fsl/tests/test_base.py,sha256=gNAc1Jpg4GdHjQVRu-KdiWYybAEvzblihYz-73jT6fk,2910
nipype/interfaces/fsl/tests/test_dti.py,sha256=0f2eOXXuKvlJ2a0Cnggf6J6v7H0MK1wUfaoJPWrBG3Q,14336
nipype/interfaces/fsl/tests/test_epi.py,sha256=EKbGM0ehtPmxYy5sA4zTDTuAsDvnpWo3Lqw12p4gB2g,1170
nipype/interfaces/fsl/tests/test_maths.py,sha256=LWpDat1Q0ZoAW-erRsXHH7d-dlp1TVZ49ACMDI5ExBE,16597
nipype/interfaces/fsl/tests/test_model.py,sha256=C8w43sIs5F3bKtiPX0kNDkIY_HQn8S2Gjyw3kuTM168,2030
nipype/interfaces/fsl/tests/test_preprocess.py,sha256=WuHKPMr90WCf8koEvlzCq_dion-dD992LL9IvikOPs4,22410
nipype/interfaces/fsl/tests/test_utils.py,sha256=_mHBrbgz6mmKJbJS_KnBqzjjvrOAhANp_1BzhAkIm9k,10569
nipype/interfaces/fsl/utils.py,sha256=q71UQ2iKRbTz29fB0a78jGSXQjDYMy8LNLFa1BUz6dA,89933
nipype/interfaces/image.py,sha256=gAkEDs1Xty6MUJheSVL6jqQueBTbP2BaCxk8hjC-tTM,8141
nipype/interfaces/io.py,sha256=rWZDZqZ7SIymPK2Oei6As9u7mxELssJsI9IRuqNqLHM,107629
nipype/interfaces/matlab.py,sha256=N6VKI0gS3exeyRqPTAwf-jaUDd2PXqLp6MoynIcAswg,7801
nipype/interfaces/meshfix.py,sha256=u5Ol5fwKB3I0mv-XpuMobkFrqtZnpHzIZ0yfrlbxzWs,8543
nipype/interfaces/minc/__init__.py,sha256=oU-odwtSpt2Nh8tbYzpwDs5xVOLNMu2dEkO-uUDjsKQ,882
nipype/interfaces/minc/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/minc/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/minc/__pycache__/minc.cpython-311.pyc,,
nipype/interfaces/minc/__pycache__/testdata.cpython-311.pyc,,
nipype/interfaces/minc/base.py,sha256=8gSBsiyifojcbrZBz1YtPQOxgzAp1zFH0vluLFiIhEk,3638
nipype/interfaces/minc/minc.py,sha256=42foS158bqLjdprVZwYfbkEl4JfInobMtRWtHmNtw_s,110888
nipype/interfaces/minc/testdata.py,sha256=uy0am81UVodmC3ovyPmhM9Gd-nIsxprLyMCQ5gYs5bE,282
nipype/interfaces/minc/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/minc/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Average.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_BBox.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Beast.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_BestLinReg.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_BigAverage.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Blob.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Blur.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Calc.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Convert.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Copy.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Dump.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Extract.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Gennlxfm.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Math.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_NlpFit.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Norm.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Pik.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Resample.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Reshape.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_ToEcat.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_ToRaw.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_VolSymm.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Volcentre.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Voliso.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_Volpad.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_XfmAvg.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_XfmConcat.cpython-311.pyc,,
nipype/interfaces/minc/tests/__pycache__/test_auto_XfmInvert.cpython-311.pyc,,
nipype/interfaces/minc/tests/test_auto_Average.py,sha256=PGh4lcx-d3AfgKwwpXaPQ3mRSRTdZrvzpYyfa_pAcWo,6910
nipype/interfaces/minc/tests/test_auto_BBox.py,sha256=GlRZcXHWZADhoqDghFxX_PVSD7EEd33Zr8udBhX49OA,1847
nipype/interfaces/minc/tests/test_auto_Beast.py,sha256=ohmqA3ZFvJpX4Jb-buzq9jp8vgck0qF2hhPgcb4SOO4,2857
nipype/interfaces/minc/tests/test_auto_BestLinReg.py,sha256=JdkpjlQcckSShrGisJ7-4dNuBHdxAgukimHvQe9-wlA,1911
nipype/interfaces/minc/tests/test_auto_BigAverage.py,sha256=ptcHpHR5CMIHyvvC-J6jAuCrBUQiZNhDEV6k-aMy_28,1868
nipype/interfaces/minc/tests/test_auto_Blob.py,sha256=2Kan5LiZImFWG5uIz7TECha8nnkeKCcv-XzY64NVW9c,1474
nipype/interfaces/minc/tests/test_auto_Blur.py,sha256=5W6y0seGVREMa0T8HWTPg-JC9Kn_ct95YsEKAmkoR2g,2414
nipype/interfaces/minc/tests/test_auto_Calc.py,sha256=t98cAZYyS1ipUqBeoM7oeLcRaRZYZZ7UORnyWYx9mLs,6994
nipype/interfaces/minc/tests/test_auto_Convert.py,sha256=802sZo01j0bgykC07DJ4KqDaZKeYMLt7CTIfbZjg0c8,1575
nipype/interfaces/minc/tests/test_auto_Copy.py,sha256=vxUNPLSLZRM3UIGD8XltZgPJQra0BR8MkHMg5pHBufQ,1446
nipype/interfaces/minc/tests/test_auto_Dump.py,sha256=LgX-NrJFzc0TSZatOr5QgaNaFUw_rSROAbcK92fXKeo,2091
nipype/interfaces/minc/tests/test_auto_Extract.py,sha256=eAlH_c5HY25tvMA-xQj3B-6BL3QK5M-VXvVuf0GI9IQ,7185
nipype/interfaces/minc/tests/test_auto_Gennlxfm.py,sha256=l6dCJiu_Ml3b_rbfF1T_vXjr2fLgyd7jrJ5NUnVa8O4,1511
nipype/interfaces/minc/tests/test_auto_Math.py,sha256=DVuXwTohao0HEkbw0QMvrcKxBaQkypJBBUy4l3xUHl8,8340
nipype/interfaces/minc/tests/test_auto_NlpFit.py,sha256=1U4rYIPXpuMK63VI-e8dNXp-TOFmQKX4R5wCnILwRu4,1880
nipype/interfaces/minc/tests/test_auto_Norm.py,sha256=ybQwJNiLQjfubihoAqx9_QRR81ZsYLmO4MvrOlVM_Ic,2430
nipype/interfaces/minc/tests/test_auto_Pik.py,sha256=w5BxqR3IDN6t4wR9-vEgdDuUicVmjy9W3vP9_6xF3cg,3303
nipype/interfaces/minc/tests/test_auto_Resample.py,sha256=lFgf4qMp8EnK8kx2iD1Ck0S8Lp3sTV3g2OBR4dd6afM,10285
nipype/interfaces/minc/tests/test_auto_Reshape.py,sha256=B1sQRDi5swvHbmRJwO0ZA53DSKZWLbgInPtOAsomCEo,1443
nipype/interfaces/minc/tests/test_auto_ToEcat.py,sha256=NNn39nR0sueVXpquq5s_H6_ihsNCT8WL58elwAni9BE,2004
nipype/interfaces/minc/tests/test_auto_ToRaw.py,sha256=uayTBEnuw2lD0x4ntZx38t0IqyIgSeBUm9Iffx2-YMg,3560
nipype/interfaces/minc/tests/test_auto_VolSymm.py,sha256=PjQimUhn6wco2ruAtIhK9KnxmNikieAS_7Oy5_8vQSU,2290
nipype/interfaces/minc/tests/test_auto_Volcentre.py,sha256=rrEEUWl7HKskLD2E6Jpu2RZUoB_jLAenN_jYR_tQjKU,1588
nipype/interfaces/minc/tests/test_auto_Voliso.py,sha256=mYjrppIbRrVCeQueTUotAtV3YFXRKeoxjBRI2_nOa98,1574
nipype/interfaces/minc/tests/test_auto_Volpad.py,sha256=vo89DMVKxlzRclfAdulH5lv9DBwIcLCuTOCVD0CP9-s,1714
nipype/interfaces/minc/tests/test_auto_XfmAvg.py,sha256=mZME0UBTEf8gyyLVr1BE96JFMP06I4bXC8VcikH2wsI,1652
nipype/interfaces/minc/tests/test_auto_XfmConcat.py,sha256=HuL0shCO5hRkeYMRYLJ3Uf1Emk7mo1sne2jr7QMYhno,1445
nipype/interfaces/minc/tests/test_auto_XfmInvert.py,sha256=Csn0Of6eVQi1I3IsySjkdMSH98koFPuAVRdYeCv562g,1339
nipype/interfaces/mipav/__init__.py,sha256=ILCYN0nsPOnf0KmKXGNK5Doa_OwXZJHduFtFDuk7-EI,694
nipype/interfaces/mipav/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mipav/__pycache__/developer.cpython-311.pyc,,
nipype/interfaces/mipav/__pycache__/generate_classes.cpython-311.pyc,,
nipype/interfaces/mipav/developer.py,sha256=v_BfjkmNOSN1gaBKit3YAt0qTsKldbSNEvvWp0XHj9A,53766
nipype/interfaces/mipav/generate_classes.py,sha256=wLZTvUeE1ToExHly0OGahWiG9A2S0XRcTQ_8qIRgSEA,2508
nipype/interfaces/mipav/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/mipav/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistBrainMgdmSegmentation.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistBrainMp2rageDuraEstimation.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistBrainMp2rageSkullStripping.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistBrainPartialVolumeFilter.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistCortexSurfaceMeshInflation.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistIntensityMp2rageMasking.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistLaminarProfileCalculator.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistLaminarProfileGeometry.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistLaminarProfileSampling.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistLaminarROIAveraging.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_JistLaminarVolumetricLayering.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmImageCalculator.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmLesionToads.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmMipavReorient.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmN3.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmSPECTRE2010.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_MedicAlgorithmThresholdToBinaryMask.cpython-311.pyc,,
nipype/interfaces/mipav/tests/__pycache__/test_auto_RandomVol.cpython-311.pyc,,
nipype/interfaces/mipav/tests/test_auto_JistBrainMgdmSegmentation.py,sha256=zbFAWaW95J4KhMupStAtJNdFzR2YUsCduWV61ISzrxY,3152
nipype/interfaces/mipav/tests/test_auto_JistBrainMp2rageDuraEstimation.py,sha256=rrgkBxFkzdbm5ojrTESLQR1i1fMrrQhP-49smyen1vQ,1686
nipype/interfaces/mipav/tests/test_auto_JistBrainMp2rageSkullStripping.py,sha256=p1CFyABzihWV0gMJIxgtC0EGuK9nyu0dY8xSsp1M0KU,2315
nipype/interfaces/mipav/tests/test_auto_JistBrainPartialVolumeFilter.py,sha256=k7J2BU1GowfkCgtViCkDi89gUg0nZ3wHYM36vXGZ0bQ,1574
nipype/interfaces/mipav/tests/test_auto_JistCortexSurfaceMeshInflation.py,sha256=OKJ64-8utko10IOfCNETEfkDo7MnHxAbmyRaTf0cNno,2045
nipype/interfaces/mipav/tests/test_auto_JistIntensityMp2rageMasking.py,sha256=SLwPp2YhbEKh9OKke9kmY1w7RlZBubg_pRnXMLHyc4k,2401
nipype/interfaces/mipav/tests/test_auto_JistLaminarProfileCalculator.py,sha256=Z9h128zTinIYyO4hkXszMsFi9kFK_FmmNlOR51SetZQ,1616
nipype/interfaces/mipav/tests/test_auto_JistLaminarProfileGeometry.py,sha256=BOa9iWD7H7HnLc2Jv6T_gZCs_Zf5NWEqk4vAi9c5lFg,1741
nipype/interfaces/mipav/tests/test_auto_JistLaminarProfileSampling.py,sha256=v1SuK4NCkdiPJ9ZSwm8MRazWT2WEOD-jUVgD3q6OG4U,1830
nipype/interfaces/mipav/tests/test_auto_JistLaminarROIAveraging.py,sha256=xNDBMlyhDzDIaTHJZCfwzRqXqvqhQNmAwElyv17T1wo,1670
nipype/interfaces/mipav/tests/test_auto_JistLaminarVolumetricLayering.py,sha256=XIRmY3mgyTvHW7W3l32XrVEie_g_T4eHJ4ntiV2leQo,2530
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmImageCalculator.py,sha256=Xf_2tGRupg6Lkqa7N8K2CQrfix-MrtyCTzZjJ5Gyso8,1623
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmLesionToads.py,sha256=9HOIBWp5hdJ6hnjA7k02LKGced-xy2BHhJYfG7mloaI,4363
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmMipavReorient.py,sha256=9HATthyS6WtqhjpE3rLj6qG3HCIhPOdb1_YZYlC4NtA,1957
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmN3.py,sha256=LrfXKcMaAcFgbeMb0_1FONsF4ygDdVg4zJoBivpvXcs,2156
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmSPECTRE2010.py,sha256=KSyrUoRsEiwHQzk1bo-ubHp6y_vfeWZaqk-uEfcm8iU,5185
nipype/interfaces/mipav/tests/test_auto_MedicAlgorithmThresholdToBinaryMask.py,sha256=5w9lsJ-UFbmiJAhqiT6wiTNkUQLJJbA3ILoLekHeccw,1596
nipype/interfaces/mipav/tests/test_auto_RandomVol.py,sha256=9C__V8ZtfyKVmK0Rfn5TkMNu0pSGgmz9xdeYI3EAKnc,1870
nipype/interfaces/mixins/__init__.py,sha256=pn9cS0GNjGYIl8uITexENZEFodLH1tUlhjIaviTmLyI,176
nipype/interfaces/mixins/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mixins/__pycache__/fixheader.cpython-311.pyc,,
nipype/interfaces/mixins/__pycache__/reporting.cpython-311.pyc,,
nipype/interfaces/mixins/fixheader.py,sha256=1XmtKuvvMzQyrIZxup5PMl2-X6Vvdy7-dUCnRjgcfw8,4733
nipype/interfaces/mixins/reporting.py,sha256=wQpJl4rZE_XUA6Nbt_jChrDD_JfXj8TBiOEJFl8qZco,1890
nipype/interfaces/mixins/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/mixins/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mixins/tests/__pycache__/test_auto_CopyHeaderInterface.cpython-311.pyc,,
nipype/interfaces/mixins/tests/__pycache__/test_auto_ReportCapableInterface.cpython-311.pyc,,
nipype/interfaces/mixins/tests/test_auto_CopyHeaderInterface.py,sha256=QEOMOhU3PhKz3RrO8S3SFdjbpFPtmbbornVDwuKpJQA,380
nipype/interfaces/mixins/tests/test_auto_ReportCapableInterface.py,sha256=WREwD83i16UCJD9F9csLMMHZwLviK_d3i1-mWRYANqc,389
nipype/interfaces/mne/__init__.py,sha256=6LDtP2eXTDnOOS5-mESWq7rUJEN0AyuQHdjAFHVmVBg,129
nipype/interfaces/mne/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mne/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/mne/base.py,sha256=YImmqQnKjRdwo_TkeLnbRaNo-jcMveV567Wvbd0wMDY,4363
nipype/interfaces/mne/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/mne/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mne/tests/__pycache__/test_auto_WatershedBEM.cpython-311.pyc,,
nipype/interfaces/mne/tests/test_auto_WatershedBEM.py,sha256=bzJve7CgiMjKtXapUPrABZ0pzMvt2_Er6Qhr2ZAYKPU,1890
nipype/interfaces/mrtrix/__init__.py,sha256=peUdXYvCC35wHPvdWVdjpI-5wHy7ym_2A9JJpzcgHAI,940
nipype/interfaces/mrtrix/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mrtrix/__pycache__/convert.cpython-311.pyc,,
nipype/interfaces/mrtrix/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/mrtrix/__pycache__/tensors.cpython-311.pyc,,
nipype/interfaces/mrtrix/__pycache__/tracking.cpython-311.pyc,,
nipype/interfaces/mrtrix/convert.py,sha256=sdcP9TC0f_Yw8QcwR8grulDvIIVV90pxluAdQuFjJNc,10836
nipype/interfaces/mrtrix/preprocess.py,sha256=Nb9Ow55O2JoTiZTqc6jm1L2tsUDnNTl6d0uUMkYKe88,28605
nipype/interfaces/mrtrix/tensors.py,sha256=ddoJwvWoWjshN7-IYtbaDJ1VpORJilOmiOGRIPzXr1c,22083
nipype/interfaces/mrtrix/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/mrtrix/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_ConstrainedSphericalDeconvolution.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_DWI2SphericalHarmonicsImage.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_DWI2Tensor.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_DiffusionTensorStreamlineTrack.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Directions2Amplitude.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Erode.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_EstimateResponseForSH.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_FSL2MRTrix.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_FilterTracks.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_FindShPeaks.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_GenerateDirections.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_GenerateWhiteMatterMask.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRConvert.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRMultiply.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRTransform.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRTrix2TrackVis.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRTrixInfo.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MRTrixViewer.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_MedianFilter3D.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_ProbabilisticSphericallyDeconvolutedStreamlineTrack.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_SphericallyDeconvolutedStreamlineTrack.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_StreamlineTrack.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Tensor2ApparentDiffusion.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Tensor2FractionalAnisotropy.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Tensor2Vector.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Threshold.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/__pycache__/test_auto_Tracks2Prob.cpython-311.pyc,,
nipype/interfaces/mrtrix/tests/test_auto_ConstrainedSphericalDeconvolution.py,sha256=hN6v0hajn-iWwtJ3R3GK4g3NmAWlXy6M5FZHvyRqs04,2343
nipype/interfaces/mrtrix/tests/test_auto_DWI2SphericalHarmonicsImage.py,sha256=tN7X3Kab-ul4W7rBzYTtBzjS_DC2OVHrGIC_fhC-tV4,1543
nipype/interfaces/mrtrix/tests/test_auto_DWI2Tensor.py,sha256=ot_yLiWGXbiAF7MGxaCVbovzuIcLMFbu1soX0b3pOIU,1815
nipype/interfaces/mrtrix/tests/test_auto_DiffusionTensorStreamlineTrack.py,sha256=arqGFuOkEJ0eW6_lhy8n1ZTOa8O4tHHZi7fc9OwELXE,4091
nipype/interfaces/mrtrix/tests/test_auto_Directions2Amplitude.py,sha256=q-XT8UlRjG90bAMFHhQhXw-xxsNVrz_WpMv0fPoEc4g,1750
nipype/interfaces/mrtrix/tests/test_auto_Erode.py,sha256=PJq-1m24IT-kcx7p6hToNvuyEsQ4MZU0X4fLP42ZBIU,1425
nipype/interfaces/mrtrix/tests/test_auto_EstimateResponseForSH.py,sha256=ywumQ0IoCOEcvyBXFbmF_fB-I22RQaDbfFaR9UqgxF0,1735
nipype/interfaces/mrtrix/tests/test_auto_FSL2MRTrix.py,sha256=TNVN1a97RhuL2rTH-EIugEczN1xEWlo8MlOhF4GnI3I,1170
nipype/interfaces/mrtrix/tests/test_auto_FilterTracks.py,sha256=AtEdPt6K1IrvATkGLAybG08qjOqEoaHv3Dqjj9nT6cQ,2307
nipype/interfaces/mrtrix/tests/test_auto_FindShPeaks.py,sha256=39_TTsqX4fU8y8MY9DpE6hB1I45rVK_ridb9nluW1_o,1928
nipype/interfaces/mrtrix/tests/test_auto_GenerateDirections.py,sha256=gstROHOUPijZPC9V7C8olBmpbkMku-Ovg0_75mBMpOw,1544
nipype/interfaces/mrtrix/tests/test_auto_GenerateWhiteMatterMask.py,sha256=_nlTHlxCBmUsAeCjrsOXvqLnzxBgpWwkzAGe4wl6SPg,1574
nipype/interfaces/mrtrix/tests/test_auto_MRConvert.py,sha256=IdaqMc-uum0OWRRAZD3-locN90I1X2Bu-NPNNl-1PUc,2128
nipype/interfaces/mrtrix/tests/test_auto_MRMultiply.py,sha256=zxW5bgxiEJF-YHNmAhJoB_iPJQRfnpffgSE0h2Oektk,1262
nipype/interfaces/mrtrix/tests/test_auto_MRTransform.py,sha256=65rqoeg-W743U4mRRDWGt5nrcuwuP5i4COrYxozs4fU,2057
nipype/interfaces/mrtrix/tests/test_auto_MRTrix2TrackVis.py,sha256=7q7JltXa8JVkzuC_kdlcV0TZ6vE9Dungj_MmNUGrZYM,1140
nipype/interfaces/mrtrix/tests/test_auto_MRTrixInfo.py,sha256=6t2z90IX7aOw1U1K2kAo6ozHucU_EMJawtdqIxsScWE,911
nipype/interfaces/mrtrix/tests/test_auto_MRTrixViewer.py,sha256=6Q6sPvXjOYjTsywYbZg2ZVNYfaj7cU6xigsmdDE6kKk,1061
nipype/interfaces/mrtrix/tests/test_auto_MedianFilter3D.py,sha256=MNcDNv-qzAaiXj3wCAy_dLWN-dAAna2l7FF0WKLwvtU,1310
nipype/interfaces/mrtrix/tests/test_auto_ProbabilisticSphericallyDeconvolutedStreamlineTrack.py,sha256=TlOHaWCVmrk8m99_k1LdWrsuj8Qnob-REB5N7dtBpEw,4118
nipype/interfaces/mrtrix/tests/test_auto_SphericallyDeconvolutedStreamlineTrack.py,sha256=bthNd-s784koUYfN4ElEmKb3FPnLsLDd8iV_DWhC8jg,3970
nipype/interfaces/mrtrix/tests/test_auto_StreamlineTrack.py,sha256=Mx9Fw3JSuXybFaygDQKkNLXc08xYDz9Yk8NcB4Fj4mQ,3855
nipype/interfaces/mrtrix/tests/test_auto_Tensor2ApparentDiffusion.py,sha256=hqGdYPcbec83PN4IM7MJLZ2hdAg9ONaWO_tm6zNf_-s,1355
nipype/interfaces/mrtrix/tests/test_auto_Tensor2FractionalAnisotropy.py,sha256=iEqosrthIylH4_M-tRbiZr0lPVcHz8uJzOpbRp_aOqY,1369
nipype/interfaces/mrtrix/tests/test_auto_Tensor2Vector.py,sha256=3VhLij0RpXmWwsSFlqSYY9LC7hVuFBV59T56MYHzJ8E,1303
nipype/interfaces/mrtrix/tests/test_auto_Threshold.py,sha256=0I8Jgy_DOn4GEbmv77sJ82cf_XVj5HWbib0gknrwWHM,1636
nipype/interfaces/mrtrix/tests/test_auto_Tracks2Prob.py,sha256=XeuXzWuZMVNEwtJ5fSK_I8_fA6vj55E_4cR9N6VckIY,1759
nipype/interfaces/mrtrix/tracking.py,sha256=JDrr7hQ9KIvFhTUZCmrQMZwv9JZ3HyPoyxx9GP9CpFs,15898
nipype/interfaces/mrtrix3/__init__.py,sha256=4cAWp_vKEX82JklgLeSs03om-gNdTK1jzfS9YzX9c5A,872
nipype/interfaces/mrtrix3/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/connectivity.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/reconst.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/tracking.cpython-311.pyc,,
nipype/interfaces/mrtrix3/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/mrtrix3/base.py,sha256=zbFXr8l-0cjANyAxFLUfHis4JM8qAwSzPUqn2z-UDlo,4054
nipype/interfaces/mrtrix3/connectivity.py,sha256=9Znuuw6Q0U8W7Csz7JOUkaefJaX-PZzy4_Q42zZiZcY,9964
nipype/interfaces/mrtrix3/preprocess.py,sha256=4DdxMRNXakT7QBwBSKkUkdRkBwJkA87dPtwQlcX6b3s,19955
nipype/interfaces/mrtrix3/reconst.py,sha256=qbyFEECzge6zgk58c5bWWU1nj7nQkF19XiwHyvAhRzs,8455
nipype/interfaces/mrtrix3/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/mrtrix3/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_ACTPrepareFSL.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_BrainMask.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_BuildConnectome.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_ComputeTDI.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_ConstrainedSphericalDeconvolution.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_DWIBiasCorrect.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_DWIDenoise.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_DWIExtract.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_DWIPreproc.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_EstimateFOD.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_FitTensor.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_Generate5tt.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_Generate5tt2gmwmi.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_LabelConfig.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_LabelConvert.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRCat.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRConvert.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRDeGibbs.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRMath.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRResize.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRTransform.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MRTrix3Base.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MTNormalise.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_MaskFilter.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_Mesh2PVE.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_ReplaceFSwithFIRST.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_ResponseSD.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_SH2Amp.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_SHConv.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_TCK2VTK.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_TensorMetrics.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_Tractography.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/__pycache__/test_auto_TransformFSLConvert.cpython-311.pyc,,
nipype/interfaces/mrtrix3/tests/test_auto_ACTPrepareFSL.py,sha256=4fR_80gVTPp6rjL3g2_vHPfYwZPnQSFRz6G1XmPtx4c,1164
nipype/interfaces/mrtrix3/tests/test_auto_BrainMask.py,sha256=HaF248Vcs802tHBMaaQGwPOS8gX69l46knQqOuGvqpY,1869
nipype/interfaces/mrtrix3/tests/test_auto_BuildConnectome.py,sha256=Raj643jLzjLD_TQkFtqlVRxMN9R3J9HQaV5kULuo5WQ,2162
nipype/interfaces/mrtrix3/tests/test_auto_ComputeTDI.py,sha256=ObhvT13UZbiDsjRMc7EOyKxDPENhZ2MjtP2IpKjbTrM,2423
nipype/interfaces/mrtrix3/tests/test_auto_ConstrainedSphericalDeconvolution.py,sha256=2NwsZzWUvUJQCcnr2KDMpuf5Y15PTohjQ4AAO2uxRLQ,3430
nipype/interfaces/mrtrix3/tests/test_auto_DWIBiasCorrect.py,sha256=A_TNr8dAC4_ArSezhleZg2ZselK-0YVP6_JGjufJYv4,2502
nipype/interfaces/mrtrix3/tests/test_auto_DWIDenoise.py,sha256=LVmJyFhsmlQMUR_PolhzWkyhhaM2O9xuTnsQGY0EFWk,2374
nipype/interfaces/mrtrix3/tests/test_auto_DWIExtract.py,sha256=RWsOKK90m5pNkrXsNyNT2ggLirvBsxFAuVviMEphgJg,2126
nipype/interfaces/mrtrix3/tests/test_auto_DWIPreproc.py,sha256=IqwWWgQLdppsNfCgQvy4AW29PL9H4MxXR0GNmy_NMjs,3505
nipype/interfaces/mrtrix3/tests/test_auto_EstimateFOD.py,sha256=dHV0hTqTqBFMeoJtVh6ZL72-xCSW8BdxtDDnMRBxmkk,3407
nipype/interfaces/mrtrix3/tests/test_auto_FitTensor.py,sha256=FZdYEFHnRP2dLEyPB-_j6Ok17KGSPaDvSEc03n1oDow,2319
nipype/interfaces/mrtrix3/tests/test_auto_Generate5tt.py,sha256=FJEDWdf7zhVVqBKlPmcgCcN7n2SUH6jPsSgDhwQMHh0,2661
nipype/interfaces/mrtrix3/tests/test_auto_Generate5tt2gmwmi.py,sha256=UWDjyioWRQWk9OV1EIOe6T8LxmelKkHDDxN6uXo6RkI,2001
nipype/interfaces/mrtrix3/tests/test_auto_LabelConfig.py,sha256=2w2mJDOyddwLYhnkWvBw09E36Gl4U8yCmCcfBzRVpoU,1858
nipype/interfaces/mrtrix3/tests/test_auto_LabelConvert.py,sha256=Bp0xAm_FCDyqMkpLsALFHctT8iu5Sy4y7UmRwGDxMLM,1603
nipype/interfaces/mrtrix3/tests/test_auto_MRCat.py,sha256=1ySxNX9FZlUeJlDjdCc7Yo5wJyOfbOb6yMTFX0I132k,1951
nipype/interfaces/mrtrix3/tests/test_auto_MRConvert.py,sha256=JxnpSMJTcqcjc6Ma5WA3QqToqZzgB7MtImFPcpBguvI,2661
nipype/interfaces/mrtrix3/tests/test_auto_MRDeGibbs.py,sha256=gufNwFFAKVt_VpBaUPaBRuh7IQYx6Hm0EwlanUsFOfo,2308
nipype/interfaces/mrtrix3/tests/test_auto_MRMath.py,sha256=hxEWVcIxb7uDgitq3o8k-MlG_N6zsqu8-HagQ2m2xCs,1999
nipype/interfaces/mrtrix3/tests/test_auto_MRResize.py,sha256=5aQbbYaiObu4iiBHHnSxDEE4gWJ5w1T3UYCI6Rcl0Sk,2467
nipype/interfaces/mrtrix3/tests/test_auto_MRTransform.py,sha256=Iq_2p93nt1O2u74FUfxeOTdP8nzSt4YDeVxcoeaixlc,2562
nipype/interfaces/mrtrix3/tests/test_auto_MRTrix3Base.py,sha256=Y3GZBIa4hYhJxvbmVCYVYJKy-xWN5-8kvXHqr8ZfrOc,498
nipype/interfaces/mrtrix3/tests/test_auto_MTNormalise.py,sha256=QBMvT-KLVlWj8wTd2phAvDt-hc2yuPpLb3X0MiRSSWA,2497
nipype/interfaces/mrtrix3/tests/test_auto_MaskFilter.py,sha256=W7M4zUYynBnIjFfr4zyP74j5zK-6u1viAWw7QEd5u2s,1353
nipype/interfaces/mrtrix3/tests/test_auto_Mesh2PVE.py,sha256=5ICtMGwgWfv1CnpBtc4kvAR2VHOKcHd8bqM0LEImIzU,1371
nipype/interfaces/mrtrix3/tests/test_auto_ReplaceFSwithFIRST.py,sha256=xv-DjvG_yUjY8RWplUEj5_B9Jxr3jM0PscG4BBm3Glw,1442
nipype/interfaces/mrtrix3/tests/test_auto_ResponseSD.py,sha256=Pg2k0ON_q-FNjEtCidHVG5S4nGknXvO1xIMBSSoCCUc,2676
nipype/interfaces/mrtrix3/tests/test_auto_SH2Amp.py,sha256=-56y4CD64-egLNlRx0GcdXWarZEVSEDRfxWk7HILWcU,1388
nipype/interfaces/mrtrix3/tests/test_auto_SHConv.py,sha256=YaapXj2Uo3rLUzO6YhD8p7aCFD2kSRL9aU74zGP_4cg,1317
nipype/interfaces/mrtrix3/tests/test_auto_TCK2VTK.py,sha256=6lhyX3fcUexOZIP6ggE1CFZMWFdD8bFhoEaUL7PSsFE,1383
nipype/interfaces/mrtrix3/tests/test_auto_TensorMetrics.py,sha256=L0VKxK9u-q6Dk7pFZsdF3SL0Ii9PITo1DbvpQ0Rne88,3310
nipype/interfaces/mrtrix3/tests/test_auto_Tractography.py,sha256=rhLKKsmfrg6mvF3pMGOk3j0Tw9sQkl5NiOXFIP_rrXE,4913
nipype/interfaces/mrtrix3/tests/test_auto_TransformFSLConvert.py,sha256=VMMJnUnmfux8fsVZEYCTUPatudpgo6oMqJ1tCRTowr0,2368
nipype/interfaces/mrtrix3/tracking.py,sha256=Q0UsMTA-4oV_662Nc8GJfyA6ZSFIDElMyWDLeU0Lq38,11982
nipype/interfaces/mrtrix3/utils.py,sha256=pmX3dQnNHb-Ddf2cHRZtQKWNSnB7SUcyIV8sr9vJPnw,42373
nipype/interfaces/niftyfit/__init__.py,sha256=kGrmy9goe8K4mzS8O2V35FV5vIl75D7O_e_nmrNxW-8,430
nipype/interfaces/niftyfit/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyfit/__pycache__/asl.cpython-311.pyc,,
nipype/interfaces/niftyfit/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/niftyfit/__pycache__/dwi.cpython-311.pyc,,
nipype/interfaces/niftyfit/__pycache__/qt1.cpython-311.pyc,,
nipype/interfaces/niftyfit/asl.py,sha256=Usrn8I-QJwlU2hJkx4hvbtanB4k4OTb6IJK7KJhHMs8,6504
nipype/interfaces/niftyfit/base.py,sha256=txHnq6qBnb6kU0UPVDYoFPeTHZQJ17cxrTLaJzF03rs,1441
nipype/interfaces/niftyfit/dwi.py,sha256=r6Xf3g31MMJkcAgEQRO1o1vjkl_dM5VUz1mlsEbq-jQ,18434
nipype/interfaces/niftyfit/qt1.py,sha256=XV9jy_f45dvTarCDApdOKpGEjEfKPs9i9uQsKWbaEig,6550
nipype/interfaces/niftyfit/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/niftyfit/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_asl.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_auto_DwiTool.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_auto_FitAsl.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_auto_FitDwi.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_auto_FitQt1.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_auto_NiftyFitCommand.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_dwi.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/__pycache__/test_qt1.cpython-311.pyc,,
nipype/interfaces/niftyfit/tests/test_asl.py,sha256=57aTJljDdhY9r6oCGUN600M4xx06OfNsIfEQO2f1xms,2019
nipype/interfaces/niftyfit/tests/test_auto_DwiTool.py,sha256=AGBGs6pe1WssKBzGmVLY5KXkHzCupQgF7T5vf_WEA98,5893
nipype/interfaces/niftyfit/tests/test_auto_FitAsl.py,sha256=9nzNSa-aqjt06enGp7E0V89ShkblyrXrqDs9SLRk5t4,3924
nipype/interfaces/niftyfit/tests/test_auto_FitDwi.py,sha256=I8pXoNUjjSlw1-OCAB6GGWjBAaKdoJh1nfrTcpUBzws,8205
nipype/interfaces/niftyfit/tests/test_auto_FitQt1.py,sha256=TWFvHHV9AvvryaXoKbmhf3yirGLZYOWzYd-RiwJmM-s,4776
nipype/interfaces/niftyfit/tests/test_auto_NiftyFitCommand.py,sha256=-HKA0Chy1RVkd47rIkOoQIk60SF5lUMhIu2nlME7Z-M,510
nipype/interfaces/niftyfit/tests/test_dwi.py,sha256=amfPbuXdxA8jwDvdbTmI3gFQ3FqrHsZtWjl6r65-WqU,3354
nipype/interfaces/niftyfit/tests/test_qt1.py,sha256=goqqLul00CuGbu186POcgb_UeKM39H32fIyj2B7FD6k,2796
nipype/interfaces/niftyreg/__init__.py,sha256=tW_sdavXkcX-ATWisuChfcB5Hs7xxdRDRC5MLLpElxw,533
nipype/interfaces/niftyreg/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyreg/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/niftyreg/__pycache__/reg.cpython-311.pyc,,
nipype/interfaces/niftyreg/__pycache__/regutils.cpython-311.pyc,,
nipype/interfaces/niftyreg/base.py,sha256=kC6GBioSgT_0QR9ux2Mig-ZAHWKmawaNj54JVxfEKRc,4722
nipype/interfaces/niftyreg/reg.py,sha256=MTbZ4ERaPI5JvRcEzrA5JjfiqV0NigdxmnYS1Hrj3zI,15293
nipype/interfaces/niftyreg/regutils.py,sha256=GwFR60h-MisHzCVJfI3Ss7MeZEOqpqvQyrQvSxzGFlc,27445
nipype/interfaces/niftyreg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/niftyreg/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_NiftyRegCommand.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegAladin.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegAverage.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegF3D.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegJacobian.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegMeasure.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegResample.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegTools.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_auto_RegTransform.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_reg.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/__pycache__/test_regutils.cpython-311.pyc,,
nipype/interfaces/niftyreg/tests/test_auto_NiftyRegCommand.py,sha256=dwAOwvcn3S3ho_LwbIxkzW2S4g1ntHn8OWbCmmXpC0Q,607
nipype/interfaces/niftyreg/tests/test_auto_RegAladin.py,sha256=Xpu8W5u8WZtQFD7Okgaard55mb0yKUr7SJ3NkikYZ8Q,3166
nipype/interfaces/niftyreg/tests/test_auto_RegAverage.py,sha256=6FmGQAeMf1ZcVZu5kbVzzdFaoSe-RbBedqtHC-ID1nU,3384
nipype/interfaces/niftyreg/tests/test_auto_RegF3D.py,sha256=yEFdesjmJH3nkvdt0xUOYoLqmLGFPZiSPGBjF_qUiDw,4889
nipype/interfaces/niftyreg/tests/test_auto_RegJacobian.py,sha256=dmlbPCEsCXYnL3cIfkn7XIMOxTaZHjY9v1qAGBflZmM,1452
nipype/interfaces/niftyreg/tests/test_auto_RegMeasure.py,sha256=no0zlzUpo1XN-TwMMuoFicNHLCgpSefROQ_yQZzdYxg,1431
nipype/interfaces/niftyreg/tests/test_auto_RegResample.py,sha256=sUyFGjuCgqq7d04PU6lcfpCHCU0X7nSqEpxx8fUAotU,1969
nipype/interfaces/niftyreg/tests/test_auto_RegTools.py,sha256=xUoO2maHBl7dNZ2x0_qh4RQP8u0ETBqN4hXgZacC_aE,2257
nipype/interfaces/niftyreg/tests/test_auto_RegTransform.py,sha256=LyHqBQ0ebaZbRvwL5zNwt3UiIdxPE7wFUBLvolaHKYw,6974
nipype/interfaces/niftyreg/tests/test_reg.py,sha256=p-EJQRL9P-8x_R4xbcW-RIPIBo1Q6PHRrGTabbQwgqs,2601
nipype/interfaces/niftyreg/tests/test_regutils.py,sha256=reRBWoUxrAGR4o8UHIpX_aOTbdgYyZiFkEd9x9Bs7io,16470
nipype/interfaces/niftyseg/__init__.py,sha256=6gkRUdDBdwubhyfBRyc16n2dIizuXHDhQ-zT3Dw08I0,559
nipype/interfaces/niftyseg/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/em.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/label_fusion.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/lesions.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/maths.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/patchmatch.cpython-311.pyc,,
nipype/interfaces/niftyseg/__pycache__/stats.cpython-311.pyc,,
nipype/interfaces/niftyseg/base.py,sha256=v0pAUPn_bbUbqEKvymXaVUn8UHA_b6eTT_nOK6PViB8,1094
nipype/interfaces/niftyseg/em.py,sha256=pMddEqIwnrkZVi3qsTrCNK5lWREI0KaFPVB5UhNGfLQ,5080
nipype/interfaces/niftyseg/label_fusion.py,sha256=rz-so5XDPFoFosBEHoMAsgMokJzJ6-52MT1GBPx_8Ho,12483
nipype/interfaces/niftyseg/lesions.py,sha256=GiwMYsn4WWrXAboa5MbdzkSUoCBYb5NMeEz-npRsxaU,3944
nipype/interfaces/niftyseg/maths.py,sha256=jbt9iLieelkUvSM1lHLYbWvS8x_kCiIJf3vB4zpYtC0,19298
nipype/interfaces/niftyseg/patchmatch.py,sha256=0iGMorPsDgbX-uFlQkrNrM6QU-g3heh1rOQ47b52-dc,3384
nipype/interfaces/niftyseg/stats.py,sha256=q-faByCQ450-hKOkHe5iMFkN6GKfK1cKCNG9wt-nHaU,8071
nipype/interfaces/niftyseg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/niftyseg/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_BinaryMaths.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_BinaryMathsInteger.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_BinaryStats.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_CalcTopNCC.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_EM.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_FillLesions.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_LabelFusion.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_MathsCommand.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_Merge.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_NiftySegCommand.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_PatchMatch.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_StatsCommand.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_TupleMaths.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_UnaryMaths.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_auto_UnaryStats.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_em_interfaces.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_extra_PatchMatch.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_label_fusion.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_lesions.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_maths.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/__pycache__/test_stats.cpython-311.pyc,,
nipype/interfaces/niftyseg/tests/test_auto_BinaryMaths.py,sha256=Lzd9YCF7pMutiMYi0Y_CZtVAUNu-HaYd0p3SZAZUzbE,1895
nipype/interfaces/niftyseg/tests/test_auto_BinaryMathsInteger.py,sha256=-ywSIGtt4wbqbErBcDvYYxVRtaA78tO6CxnnxZfgPCs,1520
nipype/interfaces/niftyseg/tests/test_auto_BinaryStats.py,sha256=aCuGoipvb_fGfaF0ElqBanmMRcOt7rzdVa-lPPk66P4,1590
nipype/interfaces/niftyseg/tests/test_auto_CalcTopNCC.py,sha256=zpFYssDv18VeZJB3WfC-t7ZmmOS-W6AZbrr4tW3oD-U,1407
nipype/interfaces/niftyseg/tests/test_auto_EM.py,sha256=2X3Vcgye7CLedEEPLST2vl2bBJQPUrbj1aWdzmqoPRg,2826
nipype/interfaces/niftyseg/tests/test_auto_FillLesions.py,sha256=e8kU3COFmQ2x_N-2mSdohwnO4dYR0bsYmlFah3Gc3Cs,2110
nipype/interfaces/niftyseg/tests/test_auto_LabelFusion.py,sha256=anQu16x67Rr_Gwe3oJ4NPSHs-G3EX0ECGwPxue7jWY4,2393
nipype/interfaces/niftyseg/tests/test_auto_MathsCommand.py,sha256=NKac0v8jr__ctJJJ792MrSr0-EKpKJ6erEvzU3I88Q8,1261
nipype/interfaces/niftyseg/tests/test_auto_Merge.py,sha256=aQsvvZE8Xlt6Y8Ck_ulJmmxwoPuJslgkuEYVnyVdTFI,1403
nipype/interfaces/niftyseg/tests/test_auto_NiftySegCommand.py,sha256=kmxsuAzqAUEgIrn4P6iabxc14SXq_N1dc9BsKaTL3I8,510
nipype/interfaces/niftyseg/tests/test_auto_PatchMatch.py,sha256=bwEN5L4OGn8EwQCjRmy7ASUAKOdkGCJzkwefU0hUaBs,1786
nipype/interfaces/niftyseg/tests/test_auto_StatsCommand.py,sha256=xiaScweZWfUbUclmFFvIbDZkmyG9SwEOGThUwKIcyWk,1151
nipype/interfaces/niftyseg/tests/test_auto_TupleMaths.py,sha256=NedIcU8N1zo8vrvGsg3pKB3MiRT-nN8Ea21Rq6yjwFg,2034
nipype/interfaces/niftyseg/tests/test_auto_UnaryMaths.py,sha256=Rb5T15j7Tlt9Sh15WmB1JrEdX27ebjk9aNTdpQCnDvg,1364
nipype/interfaces/niftyseg/tests/test_auto_UnaryStats.py,sha256=ssQ-INFWhFHkQ9ao5ImsRjrTJsZ_IZl8U4kOUsTKWLk,1254
nipype/interfaces/niftyseg/tests/test_em_interfaces.py,sha256=6ZMt2XYC8kNMFnh_Rad4IevK7tTCThhWx_WrZSvAMFc,1167
nipype/interfaces/niftyseg/tests/test_extra_PatchMatch.py,sha256=k2-vR8xaodbBq8XZTtmzDv4wFwEV5hfyooO6QsX64gY,1328
nipype/interfaces/niftyseg/tests/test_label_fusion.py,sha256=SSPwRqv1hoIGyBnOHErrNXylH9sUjTi_lsKl3EPnYZo,3891
nipype/interfaces/niftyseg/tests/test_lesions.py,sha256=YoiyYvsyzaue2i4O-eln6WIyg00ozp8GWFJGxr3tYnI,1183
nipype/interfaces/niftyseg/tests/test_maths.py,sha256=zpLyKSZ2XYWvm0ID8E6_2V0W2QpQ1-mg_ENDhAHjgyc,4704
nipype/interfaces/niftyseg/tests/test_stats.py,sha256=rl4HWaiYKj9ne1_nqQ-NNE8u5sazT-VyaDn-ob2atRc,1728
nipype/interfaces/nilearn.py,sha256=x4TD7OQtUYtpt7aeyKo0lc92Fcu9SiFeFSl1Po9AroE,6266
nipype/interfaces/nipy/__init__.py,sha256=t4KtlNLSvfWzgVesLPKLjr_0abAqhz3pu14pzi58pr4,223
nipype/interfaces/nipy/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/nipy/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/nipy/__pycache__/model.cpython-311.pyc,,
nipype/interfaces/nipy/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/nipy/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/nipy/base.py,sha256=J8sgfl1NkiyQ1sYgWZlnBY1CNptWQ21MiEpbkr5Orz8,502
nipype/interfaces/nipy/model.py,sha256=uayp2n9KXedxjWoe3-Jy_gp12P9og_fUrEhe5x-Og8Q,12341
nipype/interfaces/nipy/preprocess.py,sha256=pKJJ7J59GNuRemfmXbHviRzEsbi4_lqA6YRDFZziGm8,9016
nipype/interfaces/nipy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/nipy/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_ComputeMask.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_EstimateContrast.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_FitGLM.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_NipyBaseInterface.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_Similarity.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_SpaceTimeRealigner.cpython-311.pyc,,
nipype/interfaces/nipy/tests/__pycache__/test_auto_Trim.cpython-311.pyc,,
nipype/interfaces/nipy/tests/test_auto_ComputeMask.py,sha256=P1aj-DrWWm2Tqjh2ojeDx0ZX9jesGIZtUdcu5y1aNa8,924
nipype/interfaces/nipy/tests/test_auto_EstimateContrast.py,sha256=PdLU1D9RRYCSTY3vhtCA7__wes2uXzjkpuSv00gyjvQ,1326
nipype/interfaces/nipy/tests/test_auto_FitGLM.py,sha256=VDleVJc3RJsF9YT7ee1IYP6kg5SbknuXPIUbCqMWFsI,1613
nipype/interfaces/nipy/tests/test_auto_NipyBaseInterface.py,sha256=UT2Sy8xdxSeBEt_F6Q97VniNIwOG43xsHxEr7hcKruk,369
nipype/interfaces/nipy/tests/test_auto_Similarity.py,sha256=KXJNEeL_gFth1Rx4Upkog_DK_wZNx3TbUqITx1PPDvM,1017
nipype/interfaces/nipy/tests/test_auto_SpaceTimeRealigner.py,sha256=O4ozLG2jeg2dHnOs7IQapCo76zS4LCBaR3z11vhzmk4,986
nipype/interfaces/nipy/tests/test_auto_Trim.py,sha256=0bTRxhHRjlovNMVvmyfHxIszMqZmM-JKq-BS_pFg4M4,1011
nipype/interfaces/nipy/utils.py,sha256=sWfQXUpK2LqKx9Va-e4WvFkjugNDDZ5gvn6wYEXmOgM,3301
nipype/interfaces/nitime/__init__.py,sha256=xDERXxLAJdm0if7PiTOy_zei1I_lTqj8K94DMQfmB4o,318
nipype/interfaces/nitime/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/nitime/__pycache__/analysis.cpython-311.pyc,,
nipype/interfaces/nitime/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/nitime/analysis.py,sha256=KRoOz2vS7CS0j9u-BEi5wPKVeqREt__zRxaE1N5_V2o,9559
nipype/interfaces/nitime/base.py,sha256=d4ItD9sXpAfLDdf2rTgufKA5zPgTikliGdrAf_x3NVI,258
nipype/interfaces/nitime/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/nitime/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/nitime/tests/__pycache__/test_auto_CoherenceAnalyzer.cpython-311.pyc,,
nipype/interfaces/nitime/tests/__pycache__/test_auto_NitimeBaseInterface.cpython-311.pyc,,
nipype/interfaces/nitime/tests/__pycache__/test_nitime.cpython-311.pyc,,
nipype/interfaces/nitime/tests/test_auto_CoherenceAnalyzer.py,sha256=oJvJnFxot3eEmmLx8rFFqBpMidYPt4YupPD-dgrpDVQ,1538
nipype/interfaces/nitime/tests/test_auto_NitimeBaseInterface.py,sha256=BK81S-S2okk5-NH0Ur93YsVeOPWZbqjt2PvPP3tg_UI,375
nipype/interfaces/nitime/tests/test_nitime.py,sha256=eosi_F597HukmcZ3L-dVg0VBS_ZSrm8-Ht4tk-YGTD4,2682
nipype/interfaces/petpvc.py,sha256=w1VV67d9wl9t9vjlyigskhSQ2Z2BDk_N8GlRe7Q15Og,7485
nipype/interfaces/quickshear.py,sha256=tyLc2GVmgIr6mbmLG01TF8Z1zGyBITeCZXMxxaslW3k,3110
nipype/interfaces/r.py,sha256=HF4gWT2fH4TJPypbgGpEGlTMj9x4ft_lrHkkwK4e87c,3361
nipype/interfaces/robex/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/robex/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/robex/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/robex/preprocess.py,sha256=dHwGK2GyIBtGaLZocX8qmqy99PZVI9ceDC15fWSPyuw,2054
nipype/interfaces/robex/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/robex/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/robex/tests/__pycache__/test_auto_RobexSegment.cpython-311.pyc,,
nipype/interfaces/robex/tests/test_auto_RobexSegment.py,sha256=w8WS230KrlAOjZnZrAHKaUwraHZD0bnzkNDp0aADbJo,1634
nipype/interfaces/semtools/__init__.py,sha256=6FVcXRsAoAMW1OdUpoRuwIXVFD4puAnP9o6JCyigq88,385
nipype/interfaces/semtools/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/__pycache__/converters.cpython-311.pyc,,
nipype/interfaces/semtools/__pycache__/featurecreator.cpython-311.pyc,,
nipype/interfaces/semtools/brains/__init__.py,sha256=wU6LX5i0dfI1TalzHgSICcBbYnzsVc0Ma4witEDUWJM,243
nipype/interfaces/semtools/brains/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/brains/__pycache__/classify.cpython-311.pyc,,
nipype/interfaces/semtools/brains/__pycache__/segmentation.cpython-311.pyc,,
nipype/interfaces/semtools/brains/__pycache__/utilities.cpython-311.pyc,,
nipype/interfaces/semtools/brains/classify.py,sha256=3OklpDLrI0ehMLETzaIlyzJ03qfY-WVf0whPzv-wkeU,2475
nipype/interfaces/semtools/brains/segmentation.py,sha256=JW2klx2OC4MeSlkFoqt8TvClEm7WBidHocGK4catX40,6572
nipype/interfaces/semtools/brains/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/brains/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_BRAINSPosteriorToContinuousClass.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_BRAINSTalairach.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_BRAINSTalairachMask.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_GenerateEdgeMapImage.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_GeneratePurePlugMask.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_HistogramMatchingFilter.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/__pycache__/test_auto_SimilarityIndex.cpython-311.pyc,,
nipype/interfaces/semtools/brains/tests/test_auto_BRAINSPosteriorToContinuousClass.py,sha256=MxTheBVMB3Q544ziMyU38_GDZwX1S3UB5ZNcnASmXmI,1870
nipype/interfaces/semtools/brains/tests/test_auto_BRAINSTalairach.py,sha256=AkStLsKetcaat98eIC7NvBN6FTOIXR5JsNMl1esEiOQ,1847
nipype/interfaces/semtools/brains/tests/test_auto_BRAINSTalairachMask.py,sha256=x-zvbahB-DExNzhjO8szd68RNdEJ81W6E_spopcy-mo,1477
nipype/interfaces/semtools/brains/tests/test_auto_GenerateEdgeMapImage.py,sha256=0rj2PBnojXUd47Mr3a3l7nX10kWlVA4vza373TpuDp0,1870
nipype/interfaces/semtools/brains/tests/test_auto_GeneratePurePlugMask.py,sha256=AkvV4Zv9Ap-7F-QidEoRD63nQf0LdVnOnughGwASqZk,1286
nipype/interfaces/semtools/brains/tests/test_auto_HistogramMatchingFilter.py,sha256=1xsGNjgCTg7jGwV3tDcsvJG3EVxyiR9V2DMBzz6aGlw,1901
nipype/interfaces/semtools/brains/tests/test_auto_SimilarityIndex.py,sha256=AiNfuDxnDqVK-LRlyjjBDCnSWTeekM58Q8OszSEk3Is,1241
nipype/interfaces/semtools/brains/utilities.py,sha256=7cyA0aF2cw0f9knFoyJ7dPbexYicx7kWKI93sTrR-xQ,6451
nipype/interfaces/semtools/converters.py,sha256=vomTlzgBcHY0vL64z_g4IhyiHu37Je8yORO6_VMql_8,3343
nipype/interfaces/semtools/diffusion/__init__.py,sha256=-s4xwlSzA3JZbcFiqGNjxUjHIlbZqnTsx-IBmP-nF5o,828
nipype/interfaces/semtools/diffusion/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/__pycache__/diffusion.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/__pycache__/gtract.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/__pycache__/maxcurvature.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/diffusion.py,sha256=seogyKzdFn0gLB0XaIwB3f7oAs6NhcPwk-YPuD2yDsA,29223
nipype/interfaces/semtools/diffusion/gtract.py,sha256=rUogff7RM5kZQa4RJWLwFSV6UO67QHKtOfVHcT343ww,75529
nipype/interfaces/semtools/diffusion/maxcurvature.py,sha256=P-Lw7Pmov0vMj88XyEJPyXgCrk6hpnZdkIuelx4_3mA,2377
nipype/interfaces/semtools/diffusion/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/diffusion/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_DWIConvert.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_compareTractInclusion.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_dtiaverage.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_dtiestim.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_dtiprocess.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_extractNrrdVectorIndex.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractAnisotropyMap.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractAverageBvalues.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractClipAnisotropy.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractCoRegAnatomy.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractConcatDwi.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractCopyImageOrientation.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractCoregBvalues.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractCostFastMarching.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractCreateGuideFiber.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractFastMarchingTracking.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractFiberTracking.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractImageConformity.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractInvertBSplineTransform.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractInvertDisplacementField.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractInvertRigidTransform.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractResampleAnisotropy.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractResampleB0.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractResampleCodeImage.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractResampleDWIInPlace.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractResampleFibers.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractTensor.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_gtractTransformToDisplacementField.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/__pycache__/test_auto_maxcurvature.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tests/test_auto_DWIConvert.py,sha256=4giZjm_pBCHs3O9HOjhXIwk1IA7-GcEJFMWod_O8gjA,2916
nipype/interfaces/semtools/diffusion/tests/test_auto_compareTractInclusion.py,sha256=R4XZg3I98I2dBw1jF7-yG_FzPfiZJFbqnog0YWds-tA,1544
nipype/interfaces/semtools/diffusion/tests/test_auto_dtiaverage.py,sha256=_MAfKQia_aChWXz_AKa3OEQcFL1yIgjsn-JyK_QqjyU,1160
nipype/interfaces/semtools/diffusion/tests/test_auto_dtiestim.py,sha256=Ffigu6QBp1UGPacbq8mCaoBa08jAA3zmlDcIoGj5oEc,2550
nipype/interfaces/semtools/diffusion/tests/test_auto_dtiprocess.py,sha256=XAYvYW0vr68DVOkOybceWOMWCFK3GiguFEW2iNI_Vp4,4730
nipype/interfaces/semtools/diffusion/tests/test_auto_extractNrrdVectorIndex.py,sha256=6R-e4wwYbHZy3H43ddb37woatDKQ4ZDm1kw9ELWJcHk,1364
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractAnisotropyMap.py,sha256=tadPArlhM3OuBPbEtH4oOYCtqILO8p6npFns80DoBF4,1275
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractAverageBvalues.py,sha256=fIeHRvJyz1gGkATXtPdfWa79bDVjKji9f9RMCOHeLk4,1356
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractClipAnisotropy.py,sha256=aUKkWCFDDZsuz8u-IKoWx7vm_fH2vefwbMLI9XfV4Io,1344
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractCoRegAnatomy.py,sha256=Q2Mgv8PO1wexF3TmIRwt2s_8v5XBGLTasexyhxX8ojQ,3062
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractConcatDwi.py,sha256=UeyGN3xJJcn3gHY1pMCMPCflC1fyAlA497Ao6LsIyrM,1213
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractCopyImageOrientation.py,sha256=7xSY8BeBpG17-JBFjuC7kDiVI7_JOKuxWwkdwwhUWFc,1339
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractCoregBvalues.py,sha256=joFkrHN5gFsRELXO0FlSHXya9LOAjpLsLKtYI8S089c,2411
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractCostFastMarching.py,sha256=pRjMBbhggV0XeqFtKIToqK21enxu6T9674AtzaDBL6M,2018
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractCreateGuideFiber.py,sha256=u-sqHFYiufrmGTNz6K2GtzG5FOCZJsORmspPYoXr8GI,1365
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractFastMarchingTracking.py,sha256=RkGYFtit5X_gx4QOyQK3HOn74bk-nRl-asbDJPCP58U,2296
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractFiberTracking.py,sha256=CT-511WIHolbRrP7IVhmzyt7ZrQPZyN3SryW1F9sV-8,3436
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractImageConformity.py,sha256=Pad0YHndiZputY_QeP9WL9au9MsnmmbdIbbxboQVB0Q,1314
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractInvertBSplineTransform.py,sha256=_NvV115IY6tM98n_J7LB64nMzefHZvWlgSf12nDnqMY,1469
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractInvertDisplacementField.py,sha256=47U3b-bKOb6FTkkE8o5T9B5mYMB055A3Qz4VsOribao,1430
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractInvertRigidTransform.py,sha256=6vmlbMTfowfTwcXdLxuG99RwK6Gl5ACln59wiOG8MjE,1231
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractResampleAnisotropy.py,sha256=I8ec7JNcZf7Zfjoqun8FFx82PL2Ke2Rl30yS0iqZcLQ,1542
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractResampleB0.py,sha256=4eOOlyxCPkQGqYCeeuADYn9FtKF95Q4JckSgPq80kww,1558
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractResampleCodeImage.py,sha256=k6zY8G1edJUS2EZ9GZr-Lzeyk6pu5gHzZqJbm19oHyc,1523
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractResampleDWIInPlace.py,sha256=nvfzrVazjBDEb5QHGpQtoqa9sDPQaNEvSagmAUjre0c,1914
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractResampleFibers.py,sha256=Q2X_Imr_kRBy0P5mhWVDM_1iItVTm1QKJ2VTp8dHTyA,1575
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractTensor.py,sha256=NqjyfhT3wCs4GFPlYSP4YbCI5NXyRcBNxrhwwNGoH9s,1967
nipype/interfaces/semtools/diffusion/tests/test_auto_gtractTransformToDisplacementField.py,sha256=MMORT6dxnirleGtr-bWLlsCWtUqQBpP-N0IZcPhNE7E,1433
nipype/interfaces/semtools/diffusion/tests/test_auto_maxcurvature.py,sha256=hhX7IcgdMiX3JxXUlviEgyldUly820HlA2Xd_5IPWM4,1168
nipype/interfaces/semtools/diffusion/tractography/__init__.py,sha256=bUofwWdQwobrieJM3KlSMMPgE56aRu12otsK6zo7Va0,159
nipype/interfaces/semtools/diffusion/tractography/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/__pycache__/commandlineonly.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/__pycache__/fiberprocess.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/__pycache__/fibertrack.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/__pycache__/ukftractography.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/commandlineonly.py,sha256=evNi6vKTGjsx-zz9il6BO8F37qiCmiFwf_m6vNlUtkE,1793
nipype/interfaces/semtools/diffusion/tractography/fiberprocess.py,sha256=tu6nd1BQROj4gjDJNXIENYEMMVO_No4gJOAMmpANv_I,6101
nipype/interfaces/semtools/diffusion/tractography/fibertrack.py,sha256=X2lHJgfmMgvXtTyd7zTv8Q7NJcLa0VW4T6LIApFguS4,5451
nipype/interfaces/semtools/diffusion/tractography/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/diffusion/tractography/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/tests/__pycache__/test_auto_UKFTractography.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/tests/__pycache__/test_auto_fiberprocess.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/tests/__pycache__/test_auto_fiberstats.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/tests/__pycache__/test_auto_fibertrack.cpython-311.pyc,,
nipype/interfaces/semtools/diffusion/tractography/tests/test_auto_UKFTractography.py,sha256=VFTiVa28E3ZYkSgzwstzk0aeq4DqOiy9ZkfsnEDXd7Y,3572
nipype/interfaces/semtools/diffusion/tractography/tests/test_auto_fiberprocess.py,sha256=pq7sK70pzcfO0xUIY3UzWjApLGGlV1B2-rQJ79qskmk,2164
nipype/interfaces/semtools/diffusion/tractography/tests/test_auto_fiberstats.py,sha256=-I1pzf6DTbksCauyELqdcElAXAM8mSkqBZOMn1hkY6g,945
nipype/interfaces/semtools/diffusion/tractography/tests/test_auto_fibertrack.py,sha256=kyv2hp_nycAxqpWdgJ8pp2ucJPnMl48Pr04OthVwSro,1926
nipype/interfaces/semtools/diffusion/tractography/ukftractography.py,sha256=O3LgcjuqWq1o45ziaCx7NppzdoRncemLyJ8x1-b9uGo,7580
nipype/interfaces/semtools/featurecreator.py,sha256=KjkM2evwaxPm_1m0YOFp0TvRRdNkpYsSuq_Ta7Eixzs,1534
nipype/interfaces/semtools/filtering/__init__.py,sha256=PsvZSCNk-DkleUpBAIo0zSkT9oGXcHP0fqIsRHqFxIU,542
nipype/interfaces/semtools/filtering/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/__pycache__/denoising.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/__pycache__/featuredetection.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/denoising.py,sha256=ixkyUzeRphvEKa6qNF5zDRS2hG53-QSpbbn4UwcivHg,4721
nipype/interfaces/semtools/filtering/featuredetection.py,sha256=tv1Z-FB7BIsfym0WxrVkIWTu0LKhiQmhXOj7IavczxI,26494
nipype/interfaces/semtools/filtering/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/filtering/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_CannyEdge.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_CannySegmentationLevelSetImageFilter.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_DilateImage.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_DilateMask.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_DistanceMaps.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_DumpBinaryTrainingVectors.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_ErodeImage.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_FlippedDifference.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_GenerateBrainClippedImage.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_GenerateSummedGradientImage.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_GenerateTestImage.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_GradientAnisotropicDiffusionImageFilter.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_HammerAttributeCreator.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_NeighborhoodMean.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_NeighborhoodMedian.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_STAPLEAnalysis.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_TextureFromNoiseImageFilter.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_TextureMeasureFilter.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/__pycache__/test_auto_UnbiasedNonLocalMeans.cpython-311.pyc,,
nipype/interfaces/semtools/filtering/tests/test_auto_CannyEdge.py,sha256=T_O4g18BTI5m2a3ag5xDBzgbzV5TfsH9t6i9CVqj-E4,1291
nipype/interfaces/semtools/filtering/tests/test_auto_CannySegmentationLevelSetImageFilter.py,sha256=9nItQjlcS_wWnsMsgzcTtsmwkdgF5oIOQDu7uRhJl6E,1909
nipype/interfaces/semtools/filtering/tests/test_auto_DilateImage.py,sha256=fMhtcohrdcAx2RRu1vAnfztLvdRIbk6TVEM0TpVF_cw,1256
nipype/interfaces/semtools/filtering/tests/test_auto_DilateMask.py,sha256=-Rj63rIL8alYB4u15qpj6iLUY7Yd-Bj9IOS45eZ1GHI,1357
nipype/interfaces/semtools/filtering/tests/test_auto_DistanceMaps.py,sha256=UWJdaonWv0kQlm6CfHGHvbHrITWpY79bASVCs0YIrhM,1281
nipype/interfaces/semtools/filtering/tests/test_auto_DumpBinaryTrainingVectors.py,sha256=bnp_ISlmALriCKMuCGJUl-JP_27QpE5x_6mdCs8Vx2Q,1094
nipype/interfaces/semtools/filtering/tests/test_auto_ErodeImage.py,sha256=nAN0jjnTR9rgRz0LGa-fBXHeJkv9I1wbiLKyH3soaX4,1251
nipype/interfaces/semtools/filtering/tests/test_auto_FlippedDifference.py,sha256=KYbMsVYu1BgPch98FAP3t5wnVn6Ne9_NMtV6a_OnUbg,1210
nipype/interfaces/semtools/filtering/tests/test_auto_GenerateBrainClippedImage.py,sha256=SrfcYSn-tHCW75FX5mIa4Z_pvghpNoldGxB4OB8akHw,1320
nipype/interfaces/semtools/filtering/tests/test_auto_GenerateSummedGradientImage.py,sha256=rj3r-WUCllmyR8RFyoXzoGRIQnT3WKXvQCmJ-wXCIs0,1428
nipype/interfaces/semtools/filtering/tests/test_auto_GenerateTestImage.py,sha256=q_y5ZuYDOr149kd_cZELrR9ryRvSWh_KzTkPEcxkfr8,1387
nipype/interfaces/semtools/filtering/tests/test_auto_GradientAnisotropicDiffusionImageFilter.py,sha256=MtDq5EaxX6mmP0seVjoMJBKc6hxB4f87fjS9cEH96S8,1443
nipype/interfaces/semtools/filtering/tests/test_auto_HammerAttributeCreator.py,sha256=c78E5LCpKgkqCwn-QRnx2BoOXhm3_99K-qR18EwcU5E,1386
nipype/interfaces/semtools/filtering/tests/test_auto_NeighborhoodMean.py,sha256=rE28CmMU_YrATzji8spqJFWSJaEQac3unVdKcn8bgkc,1281
nipype/interfaces/semtools/filtering/tests/test_auto_NeighborhoodMedian.py,sha256=aHyPrMKwLswkz1HwjeSL0GIwdz2bt3A8Hhh_0QAEIUQ,1291
nipype/interfaces/semtools/filtering/tests/test_auto_STAPLEAnalysis.py,sha256=ww6kRW9TvIFGSl8ValqcBHDM_4tlyJo9Xjn2no0uB3Y,1148
nipype/interfaces/semtools/filtering/tests/test_auto_TextureFromNoiseImageFilter.py,sha256=fcfrdYxnytUKodlKGkLhvZ3cRX6ruqYfwClFBOTmu-s,1223
nipype/interfaces/semtools/filtering/tests/test_auto_TextureMeasureFilter.py,sha256=GV3uGEq-eL-r6Xz-L2lWRbTcuJ85-YIsNL3MBv7kdTM,1383
nipype/interfaces/semtools/filtering/tests/test_auto_UnbiasedNonLocalMeans.py,sha256=41blgu-YXR-zVud911rUis6XlKuzfQXiGI7ekgq6_X4,1494
nipype/interfaces/semtools/legacy/__init__.py,sha256=Uh6ya2LtwAGuERvm3UcNR3Fb57GS2UiSBQEHPg1mWNM,42
nipype/interfaces/semtools/legacy/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/legacy/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/semtools/legacy/registration.py,sha256=CHU31TLTAJzRZqxwFtNLTG2NNDygKUvnL5iqb9tN0k0,2373
nipype/interfaces/semtools/legacy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/legacy/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/legacy/tests/__pycache__/test_auto_scalartransform.cpython-311.pyc,,
nipype/interfaces/semtools/legacy/tests/test_auto_scalartransform.py,sha256=h9vlL50lAXJZb7bBzp6DoBVkrN29_PPiXAAAcyPISaQ,1579
nipype/interfaces/semtools/registration/__init__.py,sha256=RFKbxlgo_vnV154HAbTHTez7YuJUvU0yKSOksJcBQuw,204
nipype/interfaces/semtools/registration/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/registration/__pycache__/brainsfit.cpython-311.pyc,,
nipype/interfaces/semtools/registration/__pycache__/brainsresample.cpython-311.pyc,,
nipype/interfaces/semtools/registration/__pycache__/brainsresize.cpython-311.pyc,,
nipype/interfaces/semtools/registration/__pycache__/specialized.cpython-311.pyc,,
nipype/interfaces/semtools/registration/brainsfit.py,sha256=e-h1Kv4F9Ti65ULxyBXcPQdfYJOKirx4WNPiwjNaXNk,25009
nipype/interfaces/semtools/registration/brainsresample.py,sha256=wM2dSkWiFjnfjtWLGcv7Z0QcccWO6SOHpAw7rXU0bq0,4407
nipype/interfaces/semtools/registration/brainsresize.py,sha256=enL0q-4AiOzFCnUDDeNdbpBUji-kO625RTZuEM9zjlw,2092
nipype/interfaces/semtools/registration/specialized.py,sha256=rY2ptyYXBAxYI-ICfCB5vqFYrfIco3yPntnAjlPhJIM,23614
nipype/interfaces/semtools/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/registration/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_BRAINSDemonWarp.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_BRAINSFit.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_BRAINSResample.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_BRAINSResize.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_BRAINSTransformFromFiducials.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/__pycache__/test_auto_VBRAINSDemonWarp.cpython-311.pyc,,
nipype/interfaces/semtools/registration/tests/test_auto_BRAINSDemonWarp.py,sha256=9qpnRE6-52K875luUue03bSLl8-0dmsrFtWqX5oD6iU,5079
nipype/interfaces/semtools/registration/tests/test_auto_BRAINSFit.py,sha256=yKxp8LM5zBDHSfIG__5RoztSkmAzSwdszoBHVqNNyjQ,7615
nipype/interfaces/semtools/registration/tests/test_auto_BRAINSResample.py,sha256=Pb3BWgdhbzjkww2xT5b6WTASm20RyHAufDjnjUZYBM8,1922
nipype/interfaces/semtools/registration/tests/test_auto_BRAINSResize.py,sha256=azaRQQ9DRFb315TJudYEDBa2o94hdojyweYUhKs69W4,1216
nipype/interfaces/semtools/registration/tests/test_auto_BRAINSTransformFromFiducials.py,sha256=lYCVmR6bNQ0c4h6QRwYjqMf2KzKYlLDgzk6vGSqBsFI,1621
nipype/interfaces/semtools/registration/tests/test_auto_VBRAINSDemonWarp.py,sha256=9QOcPK3edkjuD51JrlXYiXG1RZwIhuiXSOmVAB8hEfw,5111
nipype/interfaces/semtools/segmentation/__init__.py,sha256=f1tjzYINX0ENIXS3wSpMFi_pMmAyFkE2AOb4wxmTOGY,227
nipype/interfaces/semtools/segmentation/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/__pycache__/specialized.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/specialized.py,sha256=XrDV3EaIsVVx3j4aQYIeG2N5RGG3-zNmpZQYPvu68HE,40652
nipype/interfaces/semtools/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/segmentation/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSABC.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSConstellationDetector.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSCreateLabelMapFromProbabilityMaps.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSCut.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSMultiSTAPLE.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BRAINSROIAuto.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_BinaryMaskEditorBasedOnLandmarks.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/__pycache__/test_auto_ESLR.cpython-311.pyc,,
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSABC.py,sha256=W7Bux2CJBHWjP9rPCXA9dpQVjm-is19yT4HG1iIypmQ,4327
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSConstellationDetector.py,sha256=KMuaY_ZJVg-Xsj1NWA19TsU_jQIVTKUmoBUQTghDduw,5639
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSCreateLabelMapFromProbabilityMaps.py,sha256=lYEm4bi9wtuTDMPZ5pLFx_qqqdzfne10rOaEQpYiR8U,1814
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSCut.py,sha256=_xeFDlv1IwvqD_GF0iWjJNbgpKvPzOj3onkwYh1bV24,2239
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSMultiSTAPLE.py,sha256=Q-fqg3uBbAQkRUNDUdI2vYoXbhA_jwpjuBgER9nDKQ4,1781
nipype/interfaces/semtools/segmentation/tests/test_auto_BRAINSROIAuto.py,sha256=DKmNePyQMWR-T_sDDwhsny4_NknFZsceeOmv2M8khTk,1960
nipype/interfaces/semtools/segmentation/tests/test_auto_BinaryMaskEditorBasedOnLandmarks.py,sha256=W2xFYpZTJWP9cNrtXShSpIaOIK0dGj1saKLgKfGwoxw,1838
nipype/interfaces/semtools/segmentation/tests/test_auto_ESLR.py,sha256=wKOdfweN_mz2o18ykXfY4hsNQdWXHGNO-KpTlsSoKu4,1541
nipype/interfaces/semtools/testing/__init__.py,sha256=1sZESFM9L1N5ghmuFlK-7dmwQiQDv5wzsZj-gP9S2iU,166
nipype/interfaces/semtools/testing/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/testing/__pycache__/featuredetection.cpython-311.pyc,,
nipype/interfaces/semtools/testing/__pycache__/generateaveragelmkfile.cpython-311.pyc,,
nipype/interfaces/semtools/testing/__pycache__/landmarkscompare.cpython-311.pyc,,
nipype/interfaces/semtools/testing/featuredetection.py,sha256=Lx-H9EDz_feAZUy6JjCFfagwcNupMYgsUuHtJzpRxSs,1152
nipype/interfaces/semtools/testing/generateaveragelmkfile.py,sha256=nogQIF95HjsPCcTH930W6xqJabMu9opjcVw91geU60U,1565
nipype/interfaces/semtools/testing/landmarkscompare.py,sha256=0C_-NhsGe2FQyQy2_Pb0krGhDfo83gsGNf0aZ77XReU,1277
nipype/interfaces/semtools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/tests/__pycache__/test_auto_DWICompare.cpython-311.pyc,,
nipype/interfaces/semtools/tests/__pycache__/test_auto_DWISimpleCompare.cpython-311.pyc,,
nipype/interfaces/semtools/tests/__pycache__/test_auto_GenerateCsfClippedFromClassifiedImage.cpython-311.pyc,,
nipype/interfaces/semtools/tests/test_auto_DWICompare.py,sha256=TxLMEerSuN6dX0DsNvJlZWQvrDYlak7qOOJWJ2FdyZA,985
nipype/interfaces/semtools/tests/test_auto_DWISimpleCompare.py,sha256=R-ej3qK3L7o-CMeglkEX_bZNQvbEO171LMgG7Z8cS0s,1091
nipype/interfaces/semtools/tests/test_auto_GenerateCsfClippedFromClassifiedImage.py,sha256=XKuEQATCIZ_f4jubKYJjjV_GN-u-Y_DcFjiPpZKY3kc,1213
nipype/interfaces/semtools/utilities/__init__.py,sha256=ssaCQiIm5kSc78jKPtvpx4NXcs-DCL9FiZxb62lxD3U,580
nipype/interfaces/semtools/utilities/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/__pycache__/brains.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/brains.py,sha256=szxw384l-xDGP3OaBQfPdubXx-11cDdp1w703vBicDk,47271
nipype/interfaces/semtools/utilities/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/semtools/utilities/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSAlignMSP.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSClipInferior.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSConstellationModeler.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSEyeDetector.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSInitializedControlPoints.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSLandmarkInitializer.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSLinearModelerEPCA.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSLmkTransform.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSMush.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSSnapShotWriter.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSTransformConvert.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_BRAINSTrimForegroundInDirection.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_CleanUpOverlapLabels.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_FindCenterOfBrain.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_GenerateLabelMapFromProbabilityMap.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_ImageRegionPlotter.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_JointHistogram.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_ShuffleVectorsModule.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_fcsv_to_hdf5.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_insertMidACPCpoint.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_landmarksConstellationAligner.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/__pycache__/test_auto_landmarksConstellationWeights.cpython-311.pyc,,
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSAlignMSP.py,sha256=ocGeQT5V5vgLPGERa6Q0d_UAycsK2-WwEebSZ-jenB8,2057
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSClipInferior.py,sha256=r1CYXxlTHwyJWmTc3x4O9z5n0g7IRqg-OhOLmSY5Uhs,1346
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSConstellationModeler.py,sha256=ApeJrDvAdBiyqM1Js8-RUJFcBT4WQN-MKSyJcWZ4oOs,2241
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSEyeDetector.py,sha256=2TAkQaUtF-l7iMYo7C9lPkE1DjTS7-qi6BgpeOaM9gQ,1241
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSInitializedControlPoints.py,sha256=2UwM4ngnYT962oYtJmyifwIYYCqU8H1Na8Q-wb0Gy50,1530
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSLandmarkInitializer.py,sha256=1eNo1yBhy4SgyIMH1WWd5C1haH5evyTxdlQzvmojLxQ,1448
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSLinearModelerEPCA.py,sha256=WI2yw4jveRBUX1oJQj0dKzhQiOcVQch_z9KniCo6BTQ,1033
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSLmkTransform.py,sha256=BYGYkkOvdjwhijaDlQIpat44-9dv_kmWTS4448avx20,1784
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSMush.py,sha256=3wZURcBlaJ5o-WRhm_ZbArDkpUFPc-d7T9OPp25YTT0,2578
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSSnapShotWriter.py,sha256=r-AokylF3qxEHx3N0jWHff9-S14-TMcsQk-EDocr0W4,1691
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSTransformConvert.py,sha256=zRQgXnKxUP33mywIf0ncMeDLqrfp6ZfiLo2e0qH-Y0Q,1617
nipype/interfaces/semtools/utilities/tests/test_auto_BRAINSTrimForegroundInDirection.py,sha256=yAbOrRk1fm9iar2dwca7TsuCIxCyoh1ZW8VPAOMFSLw,1669
nipype/interfaces/semtools/utilities/tests/test_auto_CleanUpOverlapLabels.py,sha256=2Ob_5oiRV4NoHSmKorzi3tYGKfEW4-zcuueG5Cj076Q,1076
nipype/interfaces/semtools/utilities/tests/test_auto_FindCenterOfBrain.py,sha256=OeM2k6urhtGF3xKftQAs5Er1G6Pc75j2KOoxh82ZxKU,2874
nipype/interfaces/semtools/utilities/tests/test_auto_GenerateLabelMapFromProbabilityMap.py,sha256=294f9n5Hov3d1_bpwMkPjHaYqyn3NJWCwUdLHSGXNb4,1247
nipype/interfaces/semtools/utilities/tests/test_auto_ImageRegionPlotter.py,sha256=ANHsZSSFtFGWwK3XVHEhSS0n3t3rhZ2rYrwUDbBX3sU,1695
nipype/interfaces/semtools/utilities/tests/test_auto_JointHistogram.py,sha256=btLonL6ig0B5S1kEacextYgmV3Z8d_48ZOcwjYrU_5A,1449
nipype/interfaces/semtools/utilities/tests/test_auto_ShuffleVectorsModule.py,sha256=atK8lw8Mf7izcYPoTGTgCxiskTCD52M3ZGZ6n9CG_g4,1252
nipype/interfaces/semtools/utilities/tests/test_auto_fcsv_to_hdf5.py,sha256=fwAMbaKnh5AxNtWS0fR0QTy0M0JBb5qQ9X_nngNDBHY,1524
nipype/interfaces/semtools/utilities/tests/test_auto_insertMidACPCpoint.py,sha256=mmWnjcNdkxAmKAsjNGJyuGvX_07b73Pt0R0pVs5sBdk,1122
nipype/interfaces/semtools/utilities/tests/test_auto_landmarksConstellationAligner.py,sha256=3WgoZRZeYsZMjuDQ-WsbTmW0Y30cWPzgkuY_Fwvb1jE,1192
nipype/interfaces/semtools/utilities/tests/test_auto_landmarksConstellationWeights.py,sha256=--nb9o56_nLaRdppkEJfkvXGzPgLz6XwsV2PUGhQEGo,1392
nipype/interfaces/slicer/__init__.py,sha256=69sgQyn8jHCnbRg3739gJGdPPe3hgB4XwIX684W5H6w,686
nipype/interfaces/slicer/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/slicer/__pycache__/converters.cpython-311.pyc,,
nipype/interfaces/slicer/__pycache__/generate_classes.cpython-311.pyc,,
nipype/interfaces/slicer/__pycache__/surface.cpython-311.pyc,,
nipype/interfaces/slicer/__pycache__/utilities.cpython-311.pyc,,
nipype/interfaces/slicer/base.py,sha256=lpTCREtri8byr5Lu_UruZTsgllqy_dEE2BRlMrJ053g,94
nipype/interfaces/slicer/converters.py,sha256=6HflFjuYVegGRqhQmaTrU61eFySnappfMAfL_Iu3pr0,6332
nipype/interfaces/slicer/diffusion/__init__.py,sha256=ST0MAlNkpkCoUrOSWBGRf1-2mgBSbEaf14IaemkLrQw,269
nipype/interfaces/slicer/diffusion/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/__pycache__/diffusion.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/diffusion.py,sha256=Tbdocom05ze6x1NA54HPDICNFs2FxoBeWJ1qbKzVBf0,25977
nipype/interfaces/slicer/diffusion/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/diffusion/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DTIexport.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DTIimport.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DWIJointRicianLMMSEFilter.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DWIRicianLMMSEFilter.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DWIToDTIEstimation.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DiffusionTensorScalarMeasurements.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_DiffusionWeightedVolumeMasking.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_ResampleDTIVolume.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/__pycache__/test_auto_TractographyLabelMapSeeding.cpython-311.pyc,,
nipype/interfaces/slicer/diffusion/tests/test_auto_DTIexport.py,sha256=p1Q1W3hmBL6wfNOsYIQ4Jem9QoOudgoGek_y81bxCiw,1092
nipype/interfaces/slicer/diffusion/tests/test_auto_DTIimport.py,sha256=OMB2e_fRGHAlB40CKb2lLAlThVTSMrvKAwzZ6PhXcNg,1168
nipype/interfaces/slicer/diffusion/tests/test_auto_DWIJointRicianLMMSEFilter.py,sha256=wbadqxjHsag6EEbxMtx-8o4a_H8C9cUKe29tSrwDTvk,1472
nipype/interfaces/slicer/diffusion/tests/test_auto_DWIRicianLMMSEFilter.py,sha256=ismPbWkKRA-HKHQpcPF-OLyFVUcqY0wMDlgrzAMlq-I,1829
nipype/interfaces/slicer/diffusion/tests/test_auto_DWIToDTIEstimation.py,sha256=1lvCl6WkcFt-SCbMlojDjvtpcNreUs5hi-fhySqrI9k,1590
nipype/interfaces/slicer/diffusion/tests/test_auto_DiffusionTensorScalarMeasurements.py,sha256=MYR6goqOnW5RqFHv7EJl66AyNTw6Or7aJ_5QIuVr3Z0,1292
nipype/interfaces/slicer/diffusion/tests/test_auto_DiffusionWeightedVolumeMasking.py,sha256=znua1ivNqs__ei9Ov2nsEqaQUKfqiMd7CesA8qEUrjg,1585
nipype/interfaces/slicer/diffusion/tests/test_auto_ResampleDTIVolume.py,sha256=CN4tKI7QOq_BiHFB1Eppq85uaevFl2YPgCmpSn2_jvk,3223
nipype/interfaces/slicer/diffusion/tests/test_auto_TractographyLabelMapSeeding.py,sha256=BCaG_0TXXeDGHMPMPpvefFhEfgiYfIXAr_mjzO4Xx10,2435
nipype/interfaces/slicer/filtering/__init__.py,sha256=JRivNTUsDFsQTTJXd_0Nfyoao_mlRjxfUfbMFaghrR8,850
nipype/interfaces/slicer/filtering/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/arithmetic.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/checkerboardfilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/denoising.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/extractskeleton.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/histogrammatching.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/imagelabelcombine.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/morphology.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/n4itkbiasfieldcorrection.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/resamplescalarvectordwivolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/thresholdscalarvolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/__pycache__/votingbinaryholefillingimagefilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/arithmetic.py,sha256=0Cmm_xwYsT-EyR9t3Jx5bylKDmvLiCe9Oprg0Dpp3-k,8963
nipype/interfaces/slicer/filtering/checkerboardfilter.py,sha256=2on0PeRIH_R_lHugsOgEZcyfcxWOghVxDM3NBChL9NA,2414
nipype/interfaces/slicer/filtering/denoising.py,sha256=-5L4j0MO8zABjq8OhfRwcOiL7LnpFWqH1pmkvICJxjg,9444
nipype/interfaces/slicer/filtering/extractskeleton.py,sha256=aw_6JJA4HqYuGJTIK5NSWRPibmqxHSeGtdo5enV9nQo,2397
nipype/interfaces/slicer/filtering/histogrammatching.py,sha256=rHR6eBgCNRPxazQcg4RXZU1r2b49xTKrfgV-OX4McXg,3142
nipype/interfaces/slicer/filtering/imagelabelcombine.py,sha256=v3nzJfF6AJA9p_JK0QofVKTvCekiQHCIYyyKuRCcLDg,1520
nipype/interfaces/slicer/filtering/morphology.py,sha256=e1GFDYLkU-yMuXL3M6Qi7g_Nx5nRMlaQSE5WnY7UH10,5333
nipype/interfaces/slicer/filtering/n4itkbiasfieldcorrection.py,sha256=mQhzFkYj3bsjx5W1NR-njNfvTvTMQ_3MTrL6vruqMzw,4971
nipype/interfaces/slicer/filtering/resamplescalarvectordwivolume.py,sha256=rNPlvm1W_fG8ykR55GPcmX2OaDuOfH0lV0YDeO-jPFQ,6190
nipype/interfaces/slicer/filtering/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/filtering/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_AddScalarVolumes.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_CastScalarVolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_CheckerBoardFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_CurvatureAnisotropicDiffusion.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_ExtractSkeleton.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_GaussianBlurImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_GradientAnisotropicDiffusion.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_GrayscaleFillHoleImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_GrayscaleGrindPeakImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_HistogramMatching.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_ImageLabelCombine.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_MaskScalarVolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_MedianImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_MultiplyScalarVolumes.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_N4ITKBiasFieldCorrection.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_ResampleScalarVectorDWIVolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_SubtractScalarVolumes.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_ThresholdScalarVolume.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/__pycache__/test_auto_VotingBinaryHoleFillingImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/filtering/tests/test_auto_AddScalarVolumes.py,sha256=ZqHdPIUZnIIu4ucQR6fvo20sHp8oSv340wGaK4sTCFo,1314
nipype/interfaces/slicer/filtering/tests/test_auto_CastScalarVolume.py,sha256=1CxA7B4-PCrsNOEYN0rSIWYhXiMS9HrZf3Wi_qqEcts,1194
nipype/interfaces/slicer/filtering/tests/test_auto_CheckerBoardFilter.py,sha256=oCrFuV_NBqlNWnaT2v3VopoSI9oBbyfF7DnkiyaE4uY,1371
nipype/interfaces/slicer/filtering/tests/test_auto_CurvatureAnisotropicDiffusion.py,sha256=jPZaV5hGVhrlo5bPLUgXCYZvXF540UTn1tyj48TMtOI,1416
nipype/interfaces/slicer/filtering/tests/test_auto_ExtractSkeleton.py,sha256=hCT8wzhe83-oUhgATsZN3McdTjsawV5Piz_K6uXO8Gs,1431
nipype/interfaces/slicer/filtering/tests/test_auto_GaussianBlurImageFilter.py,sha256=G9bhqRRDEY70lEWoK5AVi7liHmWNWtR-8F1iP7h4vgE,1230
nipype/interfaces/slicer/filtering/tests/test_auto_GradientAnisotropicDiffusion.py,sha256=qvN1XPjDyWoEo2RBccXUCOfB2zNmho7ZuCSSWvI2328,1411
nipype/interfaces/slicer/filtering/tests/test_auto_GrayscaleFillHoleImageFilter.py,sha256=Z8fNoH1-yZsWOaGjqRoQpy2ue2cDV_POzuXHmVbqgGg,1192
nipype/interfaces/slicer/filtering/tests/test_auto_GrayscaleGrindPeakImageFilter.py,sha256=y4G-vRnlKmLVZBMfUBEpOvugAee7f-PjS-W_kygvzNY,1197
nipype/interfaces/slicer/filtering/tests/test_auto_HistogramMatching.py,sha256=-X_TejxlVv5QlqSgKUgrwqkRoaUAjo4uPlp6nKDvKqc,1526
nipype/interfaces/slicer/filtering/tests/test_auto_ImageLabelCombine.py,sha256=7GQcrAAFTD_R7_dqF6ppWNh0jnov8OmbmxDxnQBxZU0,1356
nipype/interfaces/slicer/filtering/tests/test_auto_MaskScalarVolume.py,sha256=DaSm90FCjDIZzaTyrqdEhwXAREd_SJm2Szfin2wLYhA,1379
nipype/interfaces/slicer/filtering/tests/test_auto_MedianImageFilter.py,sha256=z9B5-g7g6hhgCU22y0Mh_aCQYo9-KglYPjx7__JPK-E,1235
nipype/interfaces/slicer/filtering/tests/test_auto_MultiplyScalarVolumes.py,sha256=WINTBdHthdmi3j5BE07ACyCdSfKKtDCB9qOscKVBPC8,1339
nipype/interfaces/slicer/filtering/tests/test_auto_N4ITKBiasFieldCorrection.py,sha256=C7cLj2A21goQvki6R_kwcXa6m2HZYZ5O93Gw_ZAtlzA,2168
nipype/interfaces/slicer/filtering/tests/test_auto_ResampleScalarVectorDWIVolume.py,sha256=N7vPbBcew3ygjkDQFxpHO6GShB0BB-qdDbOYo2mfvMA,3129
nipype/interfaces/slicer/filtering/tests/test_auto_SubtractScalarVolumes.py,sha256=1Znb4Y2FzGexG85uw4-UxM2u9_TSQa1ib8eWZwc5Gi8,1339
nipype/interfaces/slicer/filtering/tests/test_auto_ThresholdScalarVolume.py,sha256=yHRQ-f4R0pd5vDlCz2x0_7tV3MJ3GENk0RPuJ-bUirQ,1526
nipype/interfaces/slicer/filtering/tests/test_auto_VotingBinaryHoleFillingImageFilter.py,sha256=KdYC-T2li8qmZieGt2rJJLpfxya7sfojq_c0NWUyPgw,1569
nipype/interfaces/slicer/filtering/thresholdscalarvolume.py,sha256=VHH_km1SPrWxf-j1WYx9-w9bHBpivSDEYFk3pVoX_gI,2726
nipype/interfaces/slicer/filtering/votingbinaryholefillingimagefilter.py,sha256=9ogYwoC6du2nJlEmZt1w_Q1miEE-jxedXHz-9JOaZU8,2765
nipype/interfaces/slicer/generate_classes.py,sha256=63-7uq9ghE2fvN7itqgu_WjPjAfQPrIQPJt_C2578K4,22781
nipype/interfaces/slicer/legacy/__init__.py,sha256=NSAV28bzETeC5AF61YjXLklzbzluHS04BfCfpiWSAE8,405
nipype/interfaces/slicer/legacy/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/__pycache__/converters.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/__pycache__/filtering.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/__pycache__/registration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/__pycache__/segmentation.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/converters.py,sha256=XMvXheryOIbpqTZMveFJMpO4ozdiZ3KZZUfAFCEyaKc,1339
nipype/interfaces/slicer/legacy/diffusion/__init__.py,sha256=SooObzT26H1QKR2qREZQLcJqQF2yazYT2IakL0RtbzI,54
nipype/interfaces/slicer/legacy/diffusion/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/diffusion/__pycache__/denoising.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/diffusion/denoising.py,sha256=739A2Ra6beYfdE_ADykitFoovchPiB6m4pYs7uu4xlU,3768
nipype/interfaces/slicer/legacy/diffusion/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/legacy/diffusion/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/diffusion/tests/__pycache__/test_auto_DWIUnbiasedNonLocalMeansFilter.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/diffusion/tests/test_auto_DWIUnbiasedNonLocalMeansFilter.py,sha256=YgAahPPaaFg_FJHV1oFQtedgmgx6opHed6dGsrG8J9Q,1554
nipype/interfaces/slicer/legacy/filtering.py,sha256=70o4X1nHeKHlSpqgHS0an3NITqsKo_l9n9sEICUhAMM,5867
nipype/interfaces/slicer/legacy/registration.py,sha256=ILhKSAbTplF2g9n3dimWQMdYYioyxPzXu363XFWVxfY,30689
nipype/interfaces/slicer/legacy/segmentation.py,sha256=6kFOfdfzsvMw8bMg5m7fEZ7cW7MIILf1foqBYSXLMbs,3083
nipype/interfaces/slicer/legacy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/legacy/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_AffineRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_BSplineDeformableRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_BSplineToDeformationField.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_ExpertAutomatedRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_LinearRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_MultiResolutionAffineRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_OtsuThresholdImageFilter.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_OtsuThresholdSegmentation.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_ResampleScalarVolume.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/__pycache__/test_auto_RigidRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/legacy/tests/test_auto_AffineRegistration.py,sha256=k5uWUE86S3RsrXS0EkTjX4c6GyaBA2wdoI0HX2SEy7k,2084
nipype/interfaces/slicer/legacy/tests/test_auto_BSplineDeformableRegistration.py,sha256=pJw8O5a3Inb7_YqFO31Qasb4Frqg_nJ0JKOMurJKg9s,2330
nipype/interfaces/slicer/legacy/tests/test_auto_BSplineToDeformationField.py,sha256=en726DITiJP6CNLd_4CoQ898Znn-ieewlpvrc6VB2XE,1202
nipype/interfaces/slicer/legacy/tests/test_auto_ExpertAutomatedRegistration.py,sha256=Pv8jxNLu-YqvE5Fy6s0C8gMTqbUV1qIcnYr4TN1eQpA,3539
nipype/interfaces/slicer/legacy/tests/test_auto_LinearRegistration.py,sha256=QwkA8fWMW0EtAdN91dqafRQC6h-GuQ0EgmLdRGgDjbM,2204
nipype/interfaces/slicer/legacy/tests/test_auto_MultiResolutionAffineRegistration.py,sha256=yqAsNdBEdpiuwrbq1Cz3O3jhRtB-upsL_ab0GBQC-CQ,2076
nipype/interfaces/slicer/legacy/tests/test_auto_OtsuThresholdImageFilter.py,sha256=m9v1w04Gl-8yxLDf3cIzMT6sod_-N4mxLuR3FQAEs7o,1403
nipype/interfaces/slicer/legacy/tests/test_auto_OtsuThresholdSegmentation.py,sha256=7vwd0wTbx0CRbeMtESGBkzok_UyQAAMJNeu_AuACTN4,1501
nipype/interfaces/slicer/legacy/tests/test_auto_ResampleScalarVolume.py,sha256=vzRim2nTNeIqG2kIHFkB_DyAB865MFeeY4_H-2czFcY,1320
nipype/interfaces/slicer/legacy/tests/test_auto_RigidRegistration.py,sha256=OvQIStLRMPt2K9GbynrJ2jRG84-OShxoZyJDtNbqQV8,2273
nipype/interfaces/slicer/quantification/__init__.py,sha256=MT18Cq9SPgOtqB9yBZ_euwsrERctIddS74Lrv5rRfhQ,141
nipype/interfaces/slicer/quantification/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/__pycache__/changequantification.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/__pycache__/petstandarduptakevaluecomputation.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/changequantification.py,sha256=1vIQ_R7bEZReRNp0AwKnTGEXSDmxPORdB4DQtyygizU,2572
nipype/interfaces/slicer/quantification/petstandarduptakevaluecomputation.py,sha256=7gvLUIASNpzFS-1SQqPv4_3uPaFOx0M61-tkxubGVto,3383
nipype/interfaces/slicer/quantification/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/quantification/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/tests/__pycache__/test_auto_IntensityDifferenceMetric.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/tests/__pycache__/test_auto_PETStandardUptakeValueComputation.cpython-311.pyc,,
nipype/interfaces/slicer/quantification/tests/test_auto_IntensityDifferenceMetric.py,sha256=GgvHUIvAXuELqSU3WfNKHygfVSroCoEoPr1LjLKs65s,1801
nipype/interfaces/slicer/quantification/tests/test_auto_PETStandardUptakeValueComputation.py,sha256=X1zZz5rUxOchxRJX2BTJNT1PYdLbjJIFaOkG6448nxM,1807
nipype/interfaces/slicer/registration/__init__.py,sha256=aYEAJV2aQisvEh9E26UykwBoc97m1f3Qgwwo3JtFPzA,193
nipype/interfaces/slicer/registration/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/registration/__pycache__/brainsfit.cpython-311.pyc,,
nipype/interfaces/slicer/registration/__pycache__/brainsresample.cpython-311.pyc,,
nipype/interfaces/slicer/registration/__pycache__/specialized.cpython-311.pyc,,
nipype/interfaces/slicer/registration/brainsfit.py,sha256=iYNPT5Z_ZcS2sjxlHYIQ_0kmXW7gIlDo3jyrkSh_B0w,21472
nipype/interfaces/slicer/registration/brainsresample.py,sha256=DWL3IIlE9x0U0Jn2wRoDlw1uHZOFk3AOsM36WcHrYvk,4061
nipype/interfaces/slicer/registration/specialized.py,sha256=pG0efHCADNVy1d860BtNKwB2gDnwoHLA2Sfx50e_d7A,25623
nipype/interfaces/slicer/registration/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/registration/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_ACPCTransform.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_BRAINSDemonWarp.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_BRAINSFit.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_BRAINSResample.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_FiducialRegistration.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/__pycache__/test_auto_VBRAINSDemonWarp.cpython-311.pyc,,
nipype/interfaces/slicer/registration/tests/test_auto_ACPCTransform.py,sha256=2K5gneqojn8bAdkJSZsjZ3B2xHVvDzM7fW7_YdFif9c,1186
nipype/interfaces/slicer/registration/tests/test_auto_BRAINSDemonWarp.py,sha256=9qpnRE6-52K875luUue03bSLl8-0dmsrFtWqX5oD6iU,5079
nipype/interfaces/slicer/registration/tests/test_auto_BRAINSFit.py,sha256=Debbfxy9McNwHpP1nKe5Bs4Z8bjn9UVzC5qt-Bjh5kU,7282
nipype/interfaces/slicer/registration/tests/test_auto_BRAINSResample.py,sha256=Pb3BWgdhbzjkww2xT5b6WTASm20RyHAufDjnjUZYBM8,1922
nipype/interfaces/slicer/registration/tests/test_auto_FiducialRegistration.py,sha256=ttTGO89W5pie9p_PhOGM6a-YMEULNJd9dqjmAhnD9E0,1397
nipype/interfaces/slicer/registration/tests/test_auto_VBRAINSDemonWarp.py,sha256=9QOcPK3edkjuD51JrlXYiXG1RZwIhuiXSOmVAB8hEfw,5111
nipype/interfaces/slicer/segmentation/__init__.py,sha256=UdRNx_OBoBXn170MsiQgOtKGFSjve5ODZmBYGPMb-h4,165
nipype/interfaces/slicer/segmentation/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/__pycache__/simpleregiongrowingsegmentation.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/__pycache__/specialized.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/simpleregiongrowingsegmentation.py,sha256=uXJIw512SSsM2_a1ri2L9Ljjngm00Cc8lNbzcn6TIRs,2963
nipype/interfaces/slicer/segmentation/specialized.py,sha256=YOXV83mV1ur1t0YhkNlEcnssODjynBOnD-PaumIX5vA,12231
nipype/interfaces/slicer/segmentation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/segmentation/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/tests/__pycache__/test_auto_BRAINSROIAuto.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/tests/__pycache__/test_auto_EMSegmentCommandLine.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/tests/__pycache__/test_auto_RobustStatisticsSegmenter.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/tests/__pycache__/test_auto_SimpleRegionGrowingSegmentation.cpython-311.pyc,,
nipype/interfaces/slicer/segmentation/tests/test_auto_BRAINSROIAuto.py,sha256=vviUrgmNPnefSM69nz5xFFReA4UdHb1qyR9JgBb_VGU,1846
nipype/interfaces/slicer/segmentation/tests/test_auto_EMSegmentCommandLine.py,sha256=Q1PdIJdRSa_kxSq8UvB-8bMO2HkFZRs3ZTPf4nTgqcA,3216
nipype/interfaces/slicer/segmentation/tests/test_auto_RobustStatisticsSegmenter.py,sha256=JTozNlbg2jPaxL7VE7WKfJEGNCmPzyZ5DnfbB83HSZk,1747
nipype/interfaces/slicer/segmentation/tests/test_auto_SimpleRegionGrowingSegmentation.py,sha256=feO_KQOBEVAhxodVxucn_d_mPhkuIe65sa5_jIRdx-c,1755
nipype/interfaces/slicer/surface.py,sha256=t2vA9Nf4XMT5VM8VVcFj3UIX93FlpbjZAcI3nltn-FM,17342
nipype/interfaces/slicer/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/slicer/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_DicomToNrrdConverter.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_EMSegmentTransformToNewFormat.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_GrayscaleModelMaker.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_LabelMapSmoothing.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_MergeModels.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_ModelMaker.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_ModelToLabelMap.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_OrientScalarVolume.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_ProbeVolumeWithModel.cpython-311.pyc,,
nipype/interfaces/slicer/tests/__pycache__/test_auto_SlicerCommandLine.cpython-311.pyc,,
nipype/interfaces/slicer/tests/test_auto_DicomToNrrdConverter.py,sha256=i1JjRTnJWvi8mUnB5zLyrs3-InGPD1aSkGQ18ImXHbI,1560
nipype/interfaces/slicer/tests/test_auto_EMSegmentTransformToNewFormat.py,sha256=gRYDIIscKfaKCgIqudw9LSoF9Tb91JGuRHhr-KNbiiw,1256
nipype/interfaces/slicer/tests/test_auto_GrayscaleModelMaker.py,sha256=8aCwq74d2PygR1xoTVkRsGsrrqXkJn-jXAGfeN68pHM,1570
nipype/interfaces/slicer/tests/test_auto_LabelMapSmoothing.py,sha256=cZhsRG03DWrkcCbpZ0yVCf3TYvorcALmfBr2fRlqja0,1460
nipype/interfaces/slicer/tests/test_auto_MergeModels.py,sha256=BXaq07VxT7-rf9lVYJjh0u7BlnvlZ98FgbPcR9hmDow,1208
nipype/interfaces/slicer/tests/test_auto_ModelMaker.py,sha256=GCZd113N-uU_HFM5ZVwi5zA2dytqn-LQ26_ztmGAQJk,2201
nipype/interfaces/slicer/tests/test_auto_ModelToLabelMap.py,sha256=DCYZV8a-vupQYfpf6oXrkrOaPu-hQq-2fFwmtYhJquE,1306
nipype/interfaces/slicer/tests/test_auto_OrientScalarVolume.py,sha256=na4ghHVuSryco2Nvfcx--dUl2hSTRxVinUdZjDyYiho,1219
nipype/interfaces/slicer/tests/test_auto_ProbeVolumeWithModel.py,sha256=ReYg1yz-PKPdD5u9zjU0rutvhm_eke5HRZLoDLiVJlQ,1262
nipype/interfaces/slicer/tests/test_auto_SlicerCommandLine.py,sha256=-D_9dxh5zEkXLjpDAVHJZQjL7_l-Ob4aJyJgfNj2qIE,516
nipype/interfaces/slicer/utilities.py,sha256=yOi9bnb5L15u-Q0OEhUK38XDutmNwHiD7QwMCcocLZg,1923
nipype/interfaces/spm/__init__.py,sha256=rCj1Xd0t0BAsdnCfYR483JGfwVpMHX78s4Psocz-8AA,936
nipype/interfaces/spm/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/spm/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/spm/__pycache__/model.cpython-311.pyc,,
nipype/interfaces/spm/__pycache__/preprocess.cpython-311.pyc,,
nipype/interfaces/spm/__pycache__/utils.cpython-311.pyc,,
nipype/interfaces/spm/base.py,sha256=mpRMxQnko52t-jJPzNcjOLUn3ffhzTshBlRi3808598,21266
nipype/interfaces/spm/model.py,sha256=OgdF1vsMvHSkzZovwKnlwEDrJOnmH5AFTLkjiXq3ljw,43134
nipype/interfaces/spm/preprocess.py,sha256=5rj9tzxyUkZQBq_LgE6oCOaOROpffLo49tjVuJErpg8,103224
nipype/interfaces/spm/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/spm/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Analyze2nii.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ApplyDeformations.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ApplyInverseDeformation.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ApplyTransform.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ApplyVDM.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_CalcCoregAffine.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Coregister.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_CreateWarped.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_DARTEL.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_DARTELNorm2MNI.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_DicomImport.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_EstimateContrast.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_EstimateModel.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_FactorialDesign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_FieldMap.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Level1Design.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_MultiChannelNewSegment.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_MultipleRegressionDesign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_NewSegment.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Normalize.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Normalize12.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_OneSampleTTestDesign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_PairedTTestDesign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Realign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_RealignUnwarp.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Reslice.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ResliceToReference.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_SPMCommand.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Segment.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_SliceTiming.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Smooth.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_Threshold.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_ThresholdStatistics.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_TwoSampleTTestDesign.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_auto_VBMSegment.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_model.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_preprocess.cpython-311.pyc,,
nipype/interfaces/spm/tests/__pycache__/test_utils.cpython-311.pyc,,
nipype/interfaces/spm/tests/test_auto_Analyze2nii.py,sha256=OXMtjCZktsf4hmICfUqnrvK__KQzifPPxKa9cPvfpiw,1244
nipype/interfaces/spm/tests/test_auto_ApplyDeformations.py,sha256=XQNbRwdOt0TfezknAcrp5ThE8zytJAtyYsLnP19GUzQ,1371
nipype/interfaces/spm/tests/test_auto_ApplyInverseDeformation.py,sha256=PsHTsMFC6NNio9kLSGkvpmL-un5H8UtWDliA7zGapcE,1691
nipype/interfaces/spm/tests/test_auto_ApplyTransform.py,sha256=B3XMZRYa8V2KikOLfDirOMroUuRnXpsBc-BEYRIJhSU,1229
nipype/interfaces/spm/tests/test_auto_ApplyVDM.py,sha256=CxOrZRCVELWzX0KJtF0q-Oj-jl-56xk3lnqrDqlo8Qo,1746
nipype/interfaces/spm/tests/test_auto_CalcCoregAffine.py,sha256=1oh5-7Om-tw4eUmQ56U9YU0qypAIUbThMJE48M9c0PA,1323
nipype/interfaces/spm/tests/test_auto_Coregister.py,sha256=u0puJxtT5R1ZBUTADr39KrhEVPUWFOVHT2Y9DQtJdj8,1975
nipype/interfaces/spm/tests/test_auto_CreateWarped.py,sha256=7i4K4gaVZ9mSLsf4GS6lS1hudsH2fScnVqxG2Srj1Bo,1387
nipype/interfaces/spm/tests/test_auto_DARTEL.py,sha256=PlT6S5IaJJAsDSuYDTrc9pFy44CJCQ5NEy2bEM0EzjU,1482
nipype/interfaces/spm/tests/test_auto_DARTELNorm2MNI.py,sha256=sAiMmMvhQJigrX9XJ3iWBD1qWFBdXODyFxsCprhhjzU,1733
nipype/interfaces/spm/tests/test_auto_DicomImport.py,sha256=pjxADfVMoGam7JH3krV5LDp0JrRzoW99behm_oWDSCw,1362
nipype/interfaces/spm/tests/test_auto_EstimateContrast.py,sha256=yPiVek_0-lgv8A_31nP0foKm1VIMfmekJnjUvptlFMI,1636
nipype/interfaces/spm/tests/test_auto_EstimateModel.py,sha256=6ckLGQYBIUQDpzjCF70Pix0RINrWAPqGYy7JTNuM-Uc,1931
nipype/interfaces/spm/tests/test_auto_FactorialDesign.py,sha256=7Y-mt3jVsvRokiFq9SwIc5CprFvybntqXkppNziJBUI,2333
nipype/interfaces/spm/tests/test_auto_FieldMap.py,sha256=ImWCn8Zrvxe3HYyJMhvwFXAQ9OODdZGLe4x5m_B6O4U,3997
nipype/interfaces/spm/tests/test_auto_Level1Design.py,sha256=7GKetitsyElqFEjdvTY6HQ41nny5FU4AalEtQ9mkPtI,2013
nipype/interfaces/spm/tests/test_auto_MultiChannelNewSegment.py,sha256=lwhB3fVe_g-AhoDYcJinDx4F2zIl2-JRueyv0Uwzayo,1699
nipype/interfaces/spm/tests/test_auto_MultipleRegressionDesign.py,sha256=AZoY_s9jFgktIIVMsyUoaKqgpjuVQokvCUbQ9EkZpP0,2661
nipype/interfaces/spm/tests/test_auto_NewSegment.py,sha256=xbVVNQ1l_us6cmI5Plb8Dr1zp3ZoMuCwDI6LSHGLISc,1767
nipype/interfaces/spm/tests/test_auto_Normalize.py,sha256=gExPIc4LcJlYa1EkfEGsYhO5n_zikoaXOrWyES9LCkE,2926
nipype/interfaces/spm/tests/test_auto_Normalize12.py,sha256=OjeCSaqaC9lDZSBmUfL5c5uFrA8dIb0l53RX4Bmhg04,2548
nipype/interfaces/spm/tests/test_auto_OneSampleTTestDesign.py,sha256=BO3LDI2xTxfOUjXWdcyXFzGeiNqr8qD-cqSh6i2-gPk,2454
nipype/interfaces/spm/tests/test_auto_PairedTTestDesign.py,sha256=xLKNyNia59fBAg4yxEOM9kYMFan4RXEvYaUUzSFaZTw,2587
nipype/interfaces/spm/tests/test_auto_Realign.py,sha256=8LPQpmHUWx_LrSWuyD97BAlsoiH02cwt4HePrKrpzYE,2130
nipype/interfaces/spm/tests/test_auto_RealignUnwarp.py,sha256=oJtvZdO0h9SVL-JQQftbaZ_ijcmbSK2PNUywz2MGIWo,3179
nipype/interfaces/spm/tests/test_auto_Reslice.py,sha256=VOw6DCQD_PJr1Jtamc2SUaXf4WXndd9sNbSQJIH_AgE,1213
nipype/interfaces/spm/tests/test_auto_ResliceToReference.py,sha256=fU9XWaC4rTtcXRUlzqu_7eCFenJt7fGadrsMSEV_SBg,1332
nipype/interfaces/spm/tests/test_auto_SPMCommand.py,sha256=JxIkMur78bUqw9gzytpQOJQmwEBLsIZQ5QRHMv4wELw,578
nipype/interfaces/spm/tests/test_auto_Segment.py,sha256=dakfIJn3ZjZ0_EYcww2_7tDsANgQZBPH7pv2isg_CY4,3070
nipype/interfaces/spm/tests/test_auto_SliceTiming.py,sha256=fio3bm8iUKfKlOqP2IOirixdRwhLkO9sUHtuJWArPew,1572
nipype/interfaces/spm/tests/test_auto_Smooth.py,sha256=qHdGLqDmuSFoJnKBo_6IXHcpbgt09pgDqkOzL6Vt0pI,1263
nipype/interfaces/spm/tests/test_auto_Threshold.py,sha256=B9SEjCqXQNYr0npNPix9tbx4tuCbrgMdOR28ck6CBLU,2017
nipype/interfaces/spm/tests/test_auto_ThresholdStatistics.py,sha256=r7QYOCkpVrm1HisFvLtrxKgLQins357UZ_S_UrLhO1w,1548
nipype/interfaces/spm/tests/test_auto_TwoSampleTTestDesign.py,sha256=QCFBlONZe7T0TUmCC8pmXQvey01cm0L6JOiPk_xsuPY,2707
nipype/interfaces/spm/tests/test_auto_VBMSegment.py,sha256=U2BBdm9ghT8i9dWc0Q2qc_s5kau7weiLpfbhWVDZQtY,5269
nipype/interfaces/spm/tests/test_base.py,sha256=iPlPw4fedjynxuqiGAFpfWUaHRQ6ydlN0j4buBH6fjw,5256
nipype/interfaces/spm/tests/test_model.py,sha256=SQYr5iibbSVgnbFAa9s9AMl8BKpF22KJe5MqegupqqU,1252
nipype/interfaces/spm/tests/test_preprocess.py,sha256=LeD8IeEGNlPlqsrv4Rtcziw0kSDvGQ3yhm1haA0RBp4,3987
nipype/interfaces/spm/tests/test_utils.py,sha256=EgSk7fBS2NR_TMFTnM7NAZIw6cOQ6TcsRk7QqenV410,3219
nipype/interfaces/spm/utils.py,sha256=e6Fmhz6A5U4u5Zn0eeablU4td0XfV2dBYXXRHdGDCCY,16087
nipype/interfaces/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_BIDSDataGrabber.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Bru2.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_C3d.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_C3dAffineTool.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_CopyMeta.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_DataFinder.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_DataGrabber.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_DataSink.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Dcm2nii.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Dcm2niix.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_DcmStack.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_ExportFile.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_FreeSurferSource.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_GroupAndStack.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_IOBase.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_JSONFileGrabber.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_JSONFileSink.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_LookupMeta.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_MatlabCommand.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_MergeNifti.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_MeshFix.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_MySQLSink.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_NiftiGeneratorBase.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_NilearnBaseInterface.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_PETPVC.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Quickshear.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_RCommand.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Reorient.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_Rescale.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_S3DataGrabber.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SQLiteSink.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SSHDataGrabber.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SelectFiles.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SignalExtraction.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SlicerCommandLine.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_SplitNifti.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_XNATSink.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_auto_XNATSource.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_dcm2nii.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_extra_dcm2nii.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_image.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_io.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_matlab.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_nilearn.cpython-311.pyc,,
nipype/interfaces/tests/__pycache__/test_r.cpython-311.pyc,,
nipype/interfaces/tests/test_auto_BIDSDataGrabber.py,sha256=StrlTDAhop_hzZOeN75QM0lnTEXOLe8KBGkSiBn2z-s,1006
nipype/interfaces/tests/test_auto_Bru2.py,sha256=WwX50sPeLJ0322U_Ab1MIk7lfeI6t-6KJZBSxCyMmT0,1273
nipype/interfaces/tests/test_auto_C3d.py,sha256=NF0DhVVQ4MTBI_8ZQtlt0TxMc26eSKNdlHhq3_XJpKU,1713
nipype/interfaces/tests/test_auto_C3dAffineTool.py,sha256=zlRNFdbivwIHBO7yK2Tqh8iBasxkOtZf1KUvMzVZxEA,1424
nipype/interfaces/tests/test_auto_CopyMeta.py,sha256=kdx6p1bQElRuUjvJ6BuxbvWYDgshsppyxI3ymn2jXYM,933
nipype/interfaces/tests/test_auto_DataFinder.py,sha256=n76qJstCqG_RaiEUnFLORRvOiFfM61Bkxj-FeV7mDVY,902
nipype/interfaces/tests/test_auto_DataGrabber.py,sha256=bLot7SIdX6uUvPETWSxhVKspHK5jWc46mfktIqWJQAk,958
nipype/interfaces/tests/test_auto_DataSink.py,sha256=rigq0oDxtaaHal1-QBZAE5oQimemtx8MCIoFBRaynrM,1108
nipype/interfaces/tests/test_auto_Dcm2nii.py,sha256=fC7HrzWI_r1luqzZKO7RX9eQpMx6MvERUNxtfvV6re4,2644
nipype/interfaces/tests/test_auto_Dcm2niix.py,sha256=II1Pa8XTqqWF1giidZWXvibskyMfdAUIE4tw7S_Z2lM,2472
nipype/interfaces/tests/test_auto_DcmStack.py,sha256=YLc6U7JZA6i44Ffnzw2QgqJw27Ng0Ji2jOMEHg_n5os,1020
nipype/interfaces/tests/test_auto_ExportFile.py,sha256=Go9qmZT6R14MpHJ_Sd5ZDvlJh4Oy0Z5BQ81hXJZOVCE,964
nipype/interfaces/tests/test_auto_FreeSurferSource.py,sha256=n2u08ScfBqG-xIvN32LThg7YSj8fTJVoBz0wXKAL9no,3766
nipype/interfaces/tests/test_auto_GroupAndStack.py,sha256=eKNGmk9ORDQJLp_Ou2f2TxtBTmoOqRxx2o6b1LB5njE,1007
nipype/interfaces/tests/test_auto_IOBase.py,sha256=YA8yeBAagMcS5bauPLvq-li2btjOf5-PcaWxSM-IeU0,334
nipype/interfaces/tests/test_auto_JSONFileGrabber.py,sha256=0mU7rWFn_N_-lhfTKTT0mujNcnrFkidsDKLYGB-DHRs,733
nipype/interfaces/tests/test_auto_JSONFileSink.py,sha256=lrlGDU1rZYayzWbXRAq26JkstJIYnbE5W2w6fj9IpVw,887
nipype/interfaces/tests/test_auto_LookupMeta.py,sha256=wmq03wcB-kaQjvRTk55-yKj7n8Zov13j7ZmcRCFK1b0,780
nipype/interfaces/tests/test_auto_MatlabCommand.py,sha256=opeZmSfrfoalEC03cfKJpJMmvQhEZMH8C7vh7h3C9K4,1503
nipype/interfaces/tests/test_auto_MergeNifti.py,sha256=rVplUdRwvGXGd_eCM1SMsCF0ujx0u4VKLam2C_X45fU,924
nipype/interfaces/tests/test_auto_MeshFix.py,sha256=UeNjWzM784F-rdMt-q-zBKcrceMKxv0RcrL-1Re6hd4,3887
nipype/interfaces/tests/test_auto_MySQLSink.py,sha256=QzUVJhNG2O_MmSIHCxISs66QOC9qr-ZIXbygcvsCbEg,806
nipype/interfaces/tests/test_auto_NiftiGeneratorBase.py,sha256=QOCZGbpH9KvRKtJwla_w_VZI1VNZ67J74k6moA7Eo-k,376
nipype/interfaces/tests/test_auto_NilearnBaseInterface.py,sha256=JolbZMLfc_EmLEOgZENVgQqanvY7Ex-myutfF0IpenM,381
nipype/interfaces/tests/test_auto_PETPVC.py,sha256=JBjOkmZxhv8IxhBsy5FoN4GTfULAa9JXtjyLD7W5z70,2004
nipype/interfaces/tests/test_auto_Quickshear.py,sha256=Bi9Bm3rk7BNP-XgQpwDx5y1ASpjWEmat2YT50grQCkM,1418
nipype/interfaces/tests/test_auto_RCommand.py,sha256=CeEySLN7yhITfSW0n_pk1hSY5QLRw4lgNi_3nkzv42Y,756
nipype/interfaces/tests/test_auto_Reorient.py,sha256=_7sOqgu95MI_l6IXCS63LKTUVkWhSCh1Yd_Q492yYO4,902
nipype/interfaces/tests/test_auto_Rescale.py,sha256=l3wM796MsN2u9PKjkLqjYDlNwNi4pg2SzvgwPduyD0s,946
nipype/interfaces/tests/test_auto_S3DataGrabber.py,sha256=zOGjpSSADbip4Rhngkreowe6JU1sfVlEqWBMGZx4Whs,1142
nipype/interfaces/tests/test_auto_SQLiteSink.py,sha256=sPwHGmYe5AgHpY-UMNC9X1KGoj5PAz_lzASRPMy3ZBo,511
nipype/interfaces/tests/test_auto_SSHDataGrabber.py,sha256=sJS5OtQfy9G-TlzCY2lElJyDdAVsqAQ3Wf46pZXZavA,1335
nipype/interfaces/tests/test_auto_SelectFiles.py,sha256=mKU5QEqeQMbHl_CUFKjKcRNr4U5MUfHTP9iRQj6_KtI,860
nipype/interfaces/tests/test_auto_SignalExtraction.py,sha256=siWmY2-NMA8yLcz2w0k45OaPvS5vyoPYZgQscekK1RQ,1243
nipype/interfaces/tests/test_auto_SlicerCommandLine.py,sha256=75yUVVtwimkE2qdOiOjba3VZRtA8941msh0JpK3PW3w,833
nipype/interfaces/tests/test_auto_SplitNifti.py,sha256=W_qbN9bdxZGg3gohRU0kMA1JHgwPaEIqhrH4LqH-RqY,887
nipype/interfaces/tests/test_auto_XNATSink.py,sha256=bvOuLvk-wZAL4DOmXwCniHDzKOB2vCd5q89FjToiWw0,1125
nipype/interfaces/tests/test_auto_XNATSource.py,sha256=k4Ntn-IFwJCV2N4mFxoT2mMKg0h18BOhwc2tCO5qZh4,1073
nipype/interfaces/tests/test_dcm2nii.py,sha256=Zqmuo01rlYUZLOB3jsQFhQRLk8dSHeq_BbE-1ruhnJY,920
nipype/interfaces/tests/test_extra_dcm2nii.py,sha256=0aKTEy024lRdKvE0ShV6TawuXCKqCParS1xnP8d3_5A,1890
nipype/interfaces/tests/test_image.py,sha256=AxzYlh5bcHpcMeGilQdNS2J-r3hFbT75MHgbHxhTPyY,2557
nipype/interfaces/tests/test_io.py,sha256=Dqqw6s3EZ9dw-s6x-Q8jpug8Ju-472I1QVA43fd0Juk,22754
nipype/interfaces/tests/test_matlab.py,sha256=MXwLcMZENxR9yLJjra7L0AJvorFil8STeHEAjhtSuQw,4637
nipype/interfaces/tests/test_nilearn.py,sha256=lEXE66Q3NhqcU8CkyvDRjSdaMnva_JT84FA9qNnrjKE,7341
nipype/interfaces/tests/test_r.py,sha256=uMFqZpJjm_W7yQXKY-DOMzPmKCBGpmgOjGR7lnCFvXE,2113
nipype/interfaces/utility/__init__.py,sha256=pI35c_zIUvpcjLbG6k3XjTsIV5mp4wfDZeYScz-dkjI,374
nipype/interfaces/utility/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/utility/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/utility/__pycache__/csv.cpython-311.pyc,,
nipype/interfaces/utility/__pycache__/wrappers.cpython-311.pyc,,
nipype/interfaces/utility/base.py,sha256=1fizTzPODuXzqN4hqvX6G9wBYvaKhnR8NjJL_gDf1VA,13952
nipype/interfaces/utility/csv.py,sha256=H_B6rzfwRXxYFxsSuYzPzhMnrLREClS-kzOd_tRD364,3056
nipype/interfaces/utility/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/utility/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_AssertEqual.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_CSVReader.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_Function.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_IdentityInterface.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_Merge.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_Rename.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_Select.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_auto_Split.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_csv.cpython-311.pyc,,
nipype/interfaces/utility/tests/__pycache__/test_wrappers.cpython-311.pyc,,
nipype/interfaces/utility/tests/test_auto_AssertEqual.py,sha256=lzVmnG3F9GtUXBwbvgN1ZHABxYe_3OpM0IHJvkqagp0,536
nipype/interfaces/utility/tests/test_auto_CSVReader.py,sha256=iIws0dOoqExyfygRzC-VFSqPLwbdG5NNbqNF9G2rZvg,832
nipype/interfaces/utility/tests/test_auto_Function.py,sha256=eUKyuCxs-OTWRn-xKlT-nveSKk0qkwjeN0QkWbl84xY,683
nipype/interfaces/utility/tests/test_auto_IdentityInterface.py,sha256=s_szvriRj5v3z7COAX1BdwJGWQ0kBUfle4ijyUngK1I,653
nipype/interfaces/utility/tests/test_auto_Merge.py,sha256=dRIOQTaifq-rISaWTH3406AoEnrxFm1yqRN1_MWVczw,814
nipype/interfaces/utility/tests/test_auto_Rename.py,sha256=z2hNNtVJCI6NUCVkDSg5nlm3NPMeCJ6chMHJUkTAyjg,949
nipype/interfaces/utility/tests/test_auto_Select.py,sha256=W1zPDWbZic2I5tIf4969zmJ_kOyTb7hij1Kjn07PB1w,747
nipype/interfaces/utility/tests/test_auto_Split.py,sha256=qpn7Tdm1SaXKtbNzGcR7zvTp-6msrfP-BnprQ5Gd64g,780
nipype/interfaces/utility/tests/test_base.py,sha256=pdYOj3t_eIaP9JBVK3rznC5dYAZBxTVplYTBk2oSFHQ,2464
nipype/interfaces/utility/tests/test_csv.py,sha256=YUn_8kIuB0l27voets5nB8zLmCaTjcXI1C4JTuXR60E,2637
nipype/interfaces/utility/tests/test_wrappers.py,sha256=bY7X9Lnf9gWMGIYFE7dNELu93AnNu4SmauHkre-TWvo,3410
nipype/interfaces/utility/wrappers.py,sha256=COhK4hXsHEYrymKrS_L2Mi0UqS4_PDor-r1XHiAPCDc,5420
nipype/interfaces/vista/__init__.py,sha256=qy25C-XOt6PqsvMUE2vHLEtUfCqvSjU3wcG8nB56pTE,237
nipype/interfaces/vista/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/vista/__pycache__/vista.cpython-311.pyc,,
nipype/interfaces/vista/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/vista/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/vista/tests/__pycache__/test_auto_Vnifti2Image.cpython-311.pyc,,
nipype/interfaces/vista/tests/__pycache__/test_auto_VtoMat.cpython-311.pyc,,
nipype/interfaces/vista/tests/test_auto_Vnifti2Image.py,sha256=3o2cxpTTXZjY_Lu7kHOKehHyPsGdUFUjgzs1X3CL01c,1360
nipype/interfaces/vista/tests/test_auto_VtoMat.py,sha256=wRezVq0pRpgiNGd9kdosxPhZTKOrCoVudE5WcFwxZ8c,1212
nipype/interfaces/vista/vista.py,sha256=1kP1sh8Qu3YDkkuDTHBHbjRvAoeAP8FF2ok209laXwg,2029
nipype/interfaces/vtkbase.py,sha256=6cEUidcFR0LSwPH0QCgMzpYY4qpQJzs1jqNlt6hEsWg,1995
nipype/interfaces/workbench/__init__.py,sha256=Fw71fZtnROun0iFGGIx4Any2xitIN1PAhnZ7Q_0mJa8,277
nipype/interfaces/workbench/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/workbench/__pycache__/base.cpython-311.pyc,,
nipype/interfaces/workbench/__pycache__/cifti.cpython-311.pyc,,
nipype/interfaces/workbench/__pycache__/metric.cpython-311.pyc,,
nipype/interfaces/workbench/base.py,sha256=llAmBQFy2OmxW68L0W-oupBGMozUETiq6mzKzCorl50,1941
nipype/interfaces/workbench/cifti.py,sha256=lRAxDLwcPzjCsBMpdcSsV2diU3emafkbvCWBqU6iAa0,5321
nipype/interfaces/workbench/metric.py,sha256=9YLTcpRa6EEycBhkSQZvguJ8boO2KApX_wKludWbaTg,6682
nipype/interfaces/workbench/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/interfaces/workbench/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/interfaces/workbench/tests/__pycache__/test_auto_CiftiSmooth.cpython-311.pyc,,
nipype/interfaces/workbench/tests/__pycache__/test_auto_MetricResample.cpython-311.pyc,,
nipype/interfaces/workbench/tests/__pycache__/test_auto_WBCommand.cpython-311.pyc,,
nipype/interfaces/workbench/tests/test_auto_CiftiSmooth.py,sha256=aiNp_fbCCWPuiBAfjkEjT7XHtFWDZvFkm5H_qWO-TqU,2923
nipype/interfaces/workbench/tests/test_auto_MetricResample.py,sha256=1CrR-sr683nrclsliByGTM7UGeESkx8ZJAVfNYzRG90,2477
nipype/interfaces/workbench/tests/test_auto_WBCommand.py,sha256=Xd4Cill68eSeE8Vr2RhHPRaUisyilR59QMS15yU7kjk,492
nipype/pipeline/__init__.py,sha256=hLbUwobrf4rUf9em3CHQe5fEyKEd6LAl34QxLlJq6kM,279
nipype/pipeline/__pycache__/__init__.cpython-311.pyc,,
nipype/pipeline/engine/__init__.py,sha256=Da3P9XqfSdZUyZbA0P5qch72CbvjwMhKHxjwAJ8gV50,366
nipype/pipeline/engine/__pycache__/__init__.cpython-311.pyc,,
nipype/pipeline/engine/__pycache__/base.cpython-311.pyc,,
nipype/pipeline/engine/__pycache__/nodes.cpython-311.pyc,,
nipype/pipeline/engine/__pycache__/utils.cpython-311.pyc,,
nipype/pipeline/engine/__pycache__/workflows.cpython-311.pyc,,
nipype/pipeline/engine/base.py,sha256=0fV-XJLK-zbrSt2PNG0UqpVpPgG5X26eyy7Top1a1rw,3440
nipype/pipeline/engine/nodes.py,sha256=BFzGCKCWRa4MDetrNQbLuy-vHu3z28OMMOMTLJA0LVY,52565
nipype/pipeline/engine/report_template.html,sha256=X_XW7XMLXI-MAV7-tH9-lqBNYJxA3FMfcRUTMqpGQz8,6683
nipype/pipeline/engine/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/pipeline/engine/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_engine.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_join.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_nodes.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_utils.cpython-311.pyc,,
nipype/pipeline/engine/tests/__pycache__/test_workflows.cpython-311.pyc,,
nipype/pipeline/engine/tests/test_base.py,sha256=c8fEP9qGeJGk_nCwPF2Wb-jxDqr-9yRzW0zM-j8ppNE,2385
nipype/pipeline/engine/tests/test_engine.py,sha256=dypFSQhnu5etu59iGzxTUoUADlSshKfmuJjV9ezlRoI,23831
nipype/pipeline/engine/tests/test_join.py,sha256=mxm4J4CAtP7qhglSZqAj9Nt6MntU1thXj95ncRh6-O4,22957
nipype/pipeline/engine/tests/test_nodes.py,sha256=kXvZgO_N20HTdkocxGtdCG0dnCMLQ-ANniGPRLIVIuU,11132
nipype/pipeline/engine/tests/test_utils.py,sha256=HtbBqyqjxqrx337fYGrcrvbzGKMNNA4o0O_0SetvZeA,10995
nipype/pipeline/engine/tests/test_workflows.py,sha256=Vp1BasqvqavUbxAGXFy3PsNX_5K37zeYHS0b3DU-7gU,9299
nipype/pipeline/engine/utils.py,sha256=O_rYn4FVCWpL5vfBkZ0o7rp6JH4eNIt9E6OskbAGMgs,61265
nipype/pipeline/engine/workflows.py,sha256=qAiwdIEYhcfbkMeLzJuy2cvTvQ8DqMmH8dMXVMYUpfM,44502
nipype/pipeline/plugins/__init__.py,sha256=HimkuaMp5uRmGa50lnfa2p31LzBKruzOFJLgcm7DUys,702
nipype/pipeline/plugins/__pycache__/__init__.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/base.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/condor.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/dagman.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/debug.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/ipython.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/legacymultiproc.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/linear.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/lsf.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/multiproc.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/oar.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/pbs.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/pbsgraph.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/semaphore_singleton.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/sge.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/sgegraph.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/slurm.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/slurmgraph.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/somaflow.cpython-311.pyc,,
nipype/pipeline/plugins/__pycache__/tools.cpython-311.pyc,,
nipype/pipeline/plugins/base.py,sha256=r7-NKEsIR1hIXe_qq-9XDKzYnpCI01C2M3u4p4M5FaM,25770
nipype/pipeline/plugins/condor.py,sha256=GRA7HOW-ueRDVLGJWR9GJrY7gWmSfSoiP9ODe-c5EAU,4387
nipype/pipeline/plugins/dagman.py,sha256=G2p4ePXf5W_B5VN-umq0GJlxSwp3sHTiv8G9ZVH89Zc,8032
nipype/pipeline/plugins/debug.py,sha256=S6fwVbHwJlFK7C50vWueWmKQ_8idqQoySkYGMmydQ3Y,1132
nipype/pipeline/plugins/ipython.py,sha256=pRnN2t5fmCc8K_fpflEL0S65xF1Wfgh9pfXP58uPung,4392
nipype/pipeline/plugins/legacymultiproc.py,sha256=MKnXM9nGKP5DBbl45vyaBqZ-N5p7Qxt7ngNxdvzDi_Y,15891
nipype/pipeline/plugins/linear.py,sha256=tk_003tsIy91dNxd-mRCD7wSEOvZRkC0neZRABrxbR4,2759
nipype/pipeline/plugins/lsf.py,sha256=cbHECS0aCwFX-lD7Sz5d5mKIlIWH0tmX-YiVmW9wevU,4714
nipype/pipeline/plugins/multiproc.py,sha256=aa-gT9DnUzxLzfy3pbRnXXO_cAm0iRm2G2IF7S5elwE,15620
nipype/pipeline/plugins/oar.py,sha256=rMKjNesrFaxDb-CZm1hUc0rLTt67kGEdjE64d4D7AW4,5023
nipype/pipeline/plugins/pbs.py,sha256=yyGFHaL79CXKQS1NzEDnLtPVMN25a3pYr3xRZ7r9zFg,4053
nipype/pipeline/plugins/pbsgraph.py,sha256=4OWSbEcAX8PUTcfhn0lMtVbpDKvaxOqVn3Sxt-OG4f4,2147
nipype/pipeline/plugins/semaphore_singleton.py,sha256=s0_uVn88ZWAk_DBs7wNaTUUf3tznYSS8F3bH89OhN68,53
nipype/pipeline/plugins/sge.py,sha256=OkliX8k1qXwToUcM2XYiwH0BBj83GkzZporFih8w8xw,18797
nipype/pipeline/plugins/sgegraph.py,sha256=2t_CrdNbyUJBzD8Y4Z_2LJNK-EPQpHf6Ge60XRG7zdk,6904
nipype/pipeline/plugins/slurm.py,sha256=V7CbSpmLlWQ1sFaRhdOBFazi5YvSV-I2fikG79mTGAc,5418
nipype/pipeline/plugins/slurmgraph.py,sha256=BigCO2WXFhNWAPPUI0gMkCP68CEHlI69Do17PRws5t4,7216
nipype/pipeline/plugins/somaflow.py,sha256=9jme8bFyo5saGcdqQ3LxSdgWLPTZDNojHDgrl2HEwp0,1243
nipype/pipeline/plugins/tests/__init__.py,sha256=6qVchGNDb45mRbYlodPljt--bYDF7IkgntnXxgV2-YM,114
nipype/pipeline/plugins/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_base.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_callback.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_debug.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_legacymultiproc_nondaemon.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_linear.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_multiproc.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_oar.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_pbs.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_sgelike.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_somaflow.cpython-311.pyc,,
nipype/pipeline/plugins/tests/__pycache__/test_tools.cpython-311.pyc,,
nipype/pipeline/plugins/tests/test_base.py,sha256=q0CQuVOyAstKoqLg1Gsi3mom4SzPSQzTKMVl1tzmCsk,1017
nipype/pipeline/plugins/tests/test_callback.py,sha256=F2PadGNQZL_yEtY-AhNGFXz9S2JNdaUm6blVYxfRwCo,3533
nipype/pipeline/plugins/tests/test_debug.py,sha256=d2oaiDKykNWzqiuFLOCGr-YTALYtIxc4vt5ctgPMdRI,1333
nipype/pipeline/plugins/tests/test_legacymultiproc_nondaemon.py,sha256=biMUmGmbnCprAAK70JJYw9lWfQB04nrQgFkp8yvZyWE,4399
nipype/pipeline/plugins/tests/test_linear.py,sha256=0FMFXPrdtmn8WGQoLjQ2wrskBTYXAJme7acEa_s5o-U,1285
nipype/pipeline/plugins/tests/test_multiproc.py,sha256=bbi89BZ531Eo2viY48ZrCsWEdKapsCUuk_7zHq0NuQg,4988
nipype/pipeline/plugins/tests/test_oar.py,sha256=lMHaDsSx3kHVvCMPmV8bWODCICXIDQ9NvMatHJ073sA,1401
nipype/pipeline/plugins/tests/test_pbs.py,sha256=6iOJhP5a5EBrl3dX_NULW26TKmp84yi6dN8NRbuStcQ,1362
nipype/pipeline/plugins/tests/test_sgelike.py,sha256=palw89e9oW7vu7bQHeRKfOEH66PDeFUFGSYOWUMPurg,1127
nipype/pipeline/plugins/tests/test_somaflow.py,sha256=0IO_g4Siz2eCdWgcCLlaaxjE1yTXqu-NpDJ07S8BXUU,1405
nipype/pipeline/plugins/tests/test_tools.py,sha256=OLppmLTINlsPjX-2Ut2-JYEaHBZJUBWiB8YfpXZ_5lw,1746
nipype/pipeline/plugins/tools.py,sha256=0ta_0wbGqms3hnTbb8Llbtsrh9jIPZIKDqBZa8PfJvA,6010
nipype/pkg_info.py,sha256=KiCebfKF5W_4cQMld0jc5UHs6yRus03vKUDh6T6pU6M,3109
nipype/pytest.ini,sha256=bwpNmvUCJ6V-Lw-QKto5y2ejhCchWizkE-vd3dtynFk,212
nipype/refs.py,sha256=6xFEgW-X5oNd6RTAtAMxtUtu6uMODFe-Dij4Gu_AifQ,674
nipype/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/scripts/__pycache__/__init__.cpython-311.pyc,,
nipype/scripts/__pycache__/cli.cpython-311.pyc,,
nipype/scripts/__pycache__/crash_files.cpython-311.pyc,,
nipype/scripts/__pycache__/instance.cpython-311.pyc,,
nipype/scripts/__pycache__/utils.cpython-311.pyc,,
nipype/scripts/cli.py,sha256=F8IrZ-W_itz6HM-PTNU-y3OpWBXBynUdix1EMiBJ0v4,7518
nipype/scripts/crash_files.py,sha256=TAKu3a04dYG4wQ2EZYOSZDaRSbORrklctQOLPkMgOR8,2147
nipype/scripts/instance.py,sha256=qrmcVcjTt6_VNo5SfualO5lMG1-ubMLr4EX_6KPtkAY,1313
nipype/scripts/utils.py,sha256=Wb2Y8LXHCMRJ5bhzPiExC6A_CGNjoIQRxeIvIDydlXc,4384
nipype/sphinxext/__init__.py,sha256=sx4Np6c9Eq09_tqjmWAsSWDJ1SoxDRqds2UiJ8R38dY,136
nipype/sphinxext/__pycache__/__init__.cpython-311.pyc,,
nipype/sphinxext/__pycache__/documenter.cpython-311.pyc,,
nipype/sphinxext/__pycache__/gh.cpython-311.pyc,,
nipype/sphinxext/__pycache__/plot_workflow.cpython-311.pyc,,
nipype/sphinxext/apidoc/__init__.py,sha256=s_KoyLnqwIe8pWLfDOy5KF_0As4IBv_QuW2C73wHuwA,7058
nipype/sphinxext/apidoc/__pycache__/__init__.cpython-311.pyc,,
nipype/sphinxext/apidoc/__pycache__/docstring.cpython-311.pyc,,
nipype/sphinxext/apidoc/docstring.py,sha256=CVRibENUHZfnPelSLhnyirCMGAtSdpRdgsagYeQUin0,6066
nipype/sphinxext/documenter.py,sha256=19bCvfNB7WhtQ0PpECwfeirZcn9_H5zag3LkOFpz5GM,2685
nipype/sphinxext/gh.py,sha256=LPm1cMW-pdgE3pmRdwyvYuo4CME32inZIQ4OOr_BIu0,938
nipype/sphinxext/plot_workflow.py,sha256=b1kIH--XiATqBhDsc3F4ugiaq_0BslY1pUej1oJvFBM,25014
nipype/testing/__init__.py,sha256=hItFP1Dyf65nd0ZLyXGMYgZw24KrFUSVbJckmh8Wbnc,961
nipype/testing/__pycache__/__init__.cpython-311.pyc,,
nipype/testing/__pycache__/fixtures.cpython-311.pyc,,
nipype/testing/__pycache__/utils.cpython-311.pyc,,
nipype/testing/data/4d_dwi.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/5tt_in.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/A.scheme,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/A_qmat.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/A_recon_params.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/AffineTransform.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/BrainSegmentationPrior01.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/BrainSegmentationPrior02.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/BrainSegmentationPrior03.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/BrainSegmentationPrior04.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/DisplacementFieldTransform.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/FLASH1.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/FLASH2.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/FLASH3.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/Fred+orig,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/FreeSurferColorLUT.txt,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
nipype/testing/data/FreeSurferColorLUT_adapted_aparc+aseg_out.pck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/MASK_average_thal_right.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/NWARP,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/PD.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ProbabilityMaskOfStudyTemplate.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/Q25_warp+tlrc.HEAD,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/QSH_peaks.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/README,sha256=h1HFOa7gQP73YYnAnHDc-BUCSgbb242iVk-XBCRQJkg,403
nipype/testing/data/ROI_scale500.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/SPM.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/SubjectA.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T1.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T1_brain.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T1map.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/T2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/TI4D.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/TPM.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/Template_1_IXI550_MNI152.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/Template_6.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/TransformParameters.0.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/afni_output.3D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/allFA.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/all_FA.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/anat_coreg.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/anatomical.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ants_Affine.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ants_Warp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ants_deformed.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/aparc+aseg.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/aseg.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/asl.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/atlas.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/b0.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/b0.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/b0_b0rev.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ballstickfit_data.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/bedpostxout/do_not_delete.txt,sha256=8c8pazSZyDMisOwXeyOvFLwmwjEyh1YSzjb4qTw0uDE,60
nipype/testing/data/brain_mask.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/brain_study_template.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/brain_track.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/brukerdir/fid,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/brukerdir/pdata/1/2dseq,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/bvals,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/bvals.scheme,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/bvecs,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/bvecs.scheme,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/c1s1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/c1s3.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/clustering.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cmatrix.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/complex.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/config.ini,sha256=05ea_GfOtZ_cgya1AQOE4Jxee55oI0ix0ojEMGb6DsY,25
nipype/testing/data/cont1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cont1a.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cont2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cont2a.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/converted.trk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope1run1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope1run2.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope2run1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cope2run2.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cortex.label,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/cov_split.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/csd.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/csffod.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/data.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/db.xml,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/degree.csv,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/degree.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/design.con,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/design.mat,sha256=32OSIRU6IpqF2ArwHKYou4Zlx1STIlbXhUUv22RNmT4,51
nipype/testing/data/design.txt,sha256=eFro1W2tvAGpYJ8EFX2p9tjG-dXp83jK4BZp8rtskEw,18
nipype/testing/data/dicomdir/123456-1-1.dcm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/diffusion.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/diffusion_weighted.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dilated_wm_mask.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dirs.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dofrun1,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dofrun2,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ds003_sub-01_mc.DVARS,sha256=4TRXX7AftKqosEIdqi7N6scDcIhybAjTchLgTmRt55U,476
nipype/testing/data/ds003_sub-01_mc.nii.gz,sha256=mxumNSbmY9VWjYavivkMAQYdRdasIKcJFrVquE4k1Cg,168086
nipype/testing/data/ds003_sub-01_mc_brainmask.nii.gz,sha256=g7cDp7YDpqSq5HOgnme0O2RRzclkzMT7peeIVAooLuA,261
nipype/testing/data/ds005/filler.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dteig.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dti.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi2anat_InverseWarp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi2anat_Warp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi2anat_coreg_Affine.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi_CSD_tracked.tck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi_FA.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi_WMProb.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi_evals.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/dwi_tensor.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/elastix.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/encoding.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_acqp.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_index.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_mask.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_param.txt,sha256=_3mn1tImXwGMdS7z4Q8wa7HYEiydssHiCACR9V1M3j4,205
nipype/testing/data/epi_phasediff.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_rev.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/epi_slspec.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/events.tsv,sha256=udaECmGYDx6ZIFrOKB1Cl78Q3Mkp0f4Eu9w1kCS-Ouk,265
nipype/testing/data/f1.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/f2.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fa.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fdir00.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fdir01.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ffra00.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ffra01.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fieldmap_mag.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fieldmap_mag_brain.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fieldmap_phase_fslprepared.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/first_merged.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fitted_data1.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fitted_data2.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fixed1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fixed2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/flash_05.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/flash_30.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/flirt.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fmri_timeseries.csv,sha256=snKnqOGYHRtFQuc55SRL5Bwb_uio080iS4dgXscsL_0,66972
nipype/testing/data/fmri_timeseries_nolabels.csv,sha256=uwZWDVoG1sGUAhOvDV27ZA1rHYop1naIyjA9PjF954I,66723
nipype/testing/data/fods.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fsLUT_aparc+aseg.pck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fs_LR-deformed_to-fsaverage.L.sphere.32k_fs_LR.surf.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fs_LR.L.midthickness_va_avg.32k_fs_LR.shape.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fsaverage5.L.midthickness_va_avg.10k_fsavg_L.shape.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fsaverage5_std_sphere.L.10k_fsavg_L.surf.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/fsl_mcflirt_movpar.txt,sha256=ueyXZfChajnhfeXjBZxVLp2oadfJ13pKdoQ_wE2TERM,24975
nipype/testing/data/fsl_motion_outliers_fd.txt,sha256=Y5E872nXU42wGkM4TmP-50X58WBl2VD_zN921wKQViQ,3498
nipype/testing/data/func2anat_InverseWarp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/func2anat_coreg_Affine.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/func2anat_coreg_InverseWarp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/func_epi_1_1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/func_to_struct.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional.HEAD,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional.par,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional.rms,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional3.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional_1.dcm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/functional_2.dcm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/gmfod.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/grad.b,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/grads.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/gtmseg.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/gtmseg.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/im1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/im2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/im3.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/im_affine.aff,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/im_warp.df.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/image.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/image.v,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/indices-labels.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/indices.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/input1.xfm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/jsongrabber.txt,sha256=E2znMc-tiFYwsluNwhWdcjsWhrmz2lsRCngt_b7UFR0,38
nipype/testing/data/label.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh-pial.stl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.aparc_a2009s.freesurfer.annot,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.area.structural,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.central.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.cope1.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.cope1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.hippocampus.stl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.pbt.structural,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.pial,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.pial_converted.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.sphere.reg.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.sphere.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lh.white,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lta1.lta,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lta2.lta,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/lut_file,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/magnitude.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/maps.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mask.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mask.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mask.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mask.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mean_func.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_f1samples.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_fsamples.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_ph1samples.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_phsamples.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_th1samples.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/merged_thsamples.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_initial.xfm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_nlp.conf,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_00.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_01.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_02.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_03.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_04.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_05.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_06.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_07.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_08.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_2D_09.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_00.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_01.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_02.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_03.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_04.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_05.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_06.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_07.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_08.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/minc_test_3D_09.mnc,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mni.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mni2t1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/model.pklz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/moving.csv,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/moving1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/moving2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/mrtrix3_labelconfig.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/my_database.db,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/network0.aparc+aseg.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/network0.gpickle,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/nipype2boutiques_example.json,sha256=GpGVQdi1Jgpz2sASyrKjocS9sk4aD53Zg1tkiutPfrk,19305
nipype/testing/data/nodif_brain_mask.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/norm.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/output.csv,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/output_Composite.h5,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/pdfs.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/peak_directions.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/pet.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/pet_resliced.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/phase.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rc1s1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rc1s2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rc2s1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rc2s2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/realign_json.json,sha256=YVnwnSWjKMwiuDigql-SWMv9akecBY3zOnhN6k8lrHo,932
nipype/testing/data/ref_class0.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ref_class1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/ref_tac.dat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/register.dat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/register.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/resp.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/response.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/resting.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/resting2anat_Warp.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/resting2anat_coreg_Affine.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rgb.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh-pial.stl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.aparc_a2009s.freesurfer.annot,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.central.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.pbt.structural,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.pial,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.pial_converted.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.sphere.reg.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/rh.sphere.structural.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi01.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi01_idx.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi02.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi02_idx.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi03.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi03_idx.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi04.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi04_idx.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi05.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/roi05_idx.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/run1+orig,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/run1+orig_model,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/run1_categories.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/run2+orig,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/run2_categories.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/seed.1D,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/seed_mask.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/seed_source.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/seeds_to_M1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/seeds_to_M2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/segmentation0.nii.gz,sha256=yJ3EZehr_mg4gD7GvFvghtK7elBSRfznnGyp_bxcqP8,28514
nipype/testing/data/segmentation1.nii.gz,sha256=AN9V3AjGfvEW1ybyRbmkRsBB0fJXzqTqYQjDndokt1k,26561
nipype/testing/data/session_info.npz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sh.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/skeleton_mask.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/smri_ants_registration_settings.json,sha256=KlRJwsDSxwa6rA7T3TJvbgefrchO5YSdxWb9NZecUBQ,2852
nipype/testing/data/spmT_0001.img,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/spminfo,sha256=vU-dYl7bVkIndIi-ikgLrHd1uugihbrBIloowm3Ny7s,475
nipype/testing/data/streamlines.trk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/struct2mni.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/struct_to_func.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/struct_to_template.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/structural.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/study_template.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01.L.midthickness.32k_fs_LR.surf.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01.R.midthickness.32k_fs_LR.surf.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_dir-LR_epi.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_dir-RL_epi.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_ses-baseline_pet.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_ses-baseline_pet_mean_reg.lta,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_task-rest.dtseries.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/sub-01_task-rest_bold_space-fsaverage5.L.func.gii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subj1.cff,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subj1.pck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subj2.cff,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subj2.pck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subjectDesign.con,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/subjectDesign.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/surf.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/surf01.vtk,sha256=dpRhzUT0-69WWWFbHnh7izygcjt4lItTXQNzoJ-dYJc,2538898
nipype/testing/data/surf1.vtk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/surf2.vtk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tac.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/targets_MASK1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/targets_MASK2.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tbss_dir/do_not_delete.txt,sha256=8c8pazSZyDMisOwXeyOvFLwmwjEyh1YSzjb4qTw0uDE,60
nipype/testing/data/tdi.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tensor_fitted_data.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/timeDesign.con,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/timeDesign.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/timeseries.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/timing.dat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tissue+air_map.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tissues.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/topup_encoding.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/topup_fieldcoef.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/topup_movpar.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tpm_00.nii.gz,sha256=BVcnQE5JnVArp_3YVqi2JUhHWSEwyrBAlEVMCFfYXfE,161167
nipype/testing/data/tpm_01.nii.gz,sha256=hkPtQb6xbK-Z2YEPzxAvkGdchfTITVg6wDSiXc5wSSk,140347
nipype/testing/data/tpm_02.nii.gz,sha256=5kRY0-rEwQWlxTNV7h8jcZbbN2fPkzuybXDczLZ5fGs,139761
nipype/testing/data/tpms_msk.nii.gz,sha256=byyKxkFDBEviUnif6oHoXqpEyYAKWJaMwz-pJJ6jiNw,20495
nipype/testing/data/track1.trk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/track2.trk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tracks.tck,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tracks.trk,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tract_data.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tracts.Bdouble,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/trans.mat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tst_class0.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/tst_class1.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/u_rc1s1_Template.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/u_rc1s2_Template.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/u_rc1s3_Template.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/varcope.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/varcope1run1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/varcope1run2.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/varcope2run1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/varcope2run2.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/voxel-order_data.Bfloat,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/voxeldisplacemap.vdm,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/vsm.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/warpfield.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/weights.txt,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/wm.mgz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/wm_mask.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/wm_undersampled.nii,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/wmfod.mif,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/data/zstat1.nii.gz,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/testing/fixtures.py,sha256=2d9T6R7GSMcdxyqIR6TM-_Bf6CDlQmD_V1KWBJIigP0,3146
nipype/testing/utils.py,sha256=s0UN7YIZU_1iGFJWWWaRpWZWRVlLI9b3CSaxHZ4SNAI,2926
nipype/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nipype/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/tests/__pycache__/test_nipype.cpython-311.pyc,,
nipype/tests/test_nipype.py,sha256=4nr-JfAk5fil-Iola2W4U1AsmmxgHt-IDYpcYh4oecY,3160
nipype/utils/__init__.py,sha256=tW6SlUlrq14OgvOHNv2PCv5eePNYR1GyRQDVNswaQj0,116
nipype/utils/__pycache__/__init__.cpython-311.pyc,,
nipype/utils/__pycache__/config.cpython-311.pyc,,
nipype/utils/__pycache__/datetime.cpython-311.pyc,,
nipype/utils/__pycache__/docparse.cpython-311.pyc,,
nipype/utils/__pycache__/draw_gantt_chart.cpython-311.pyc,,
nipype/utils/__pycache__/filemanip.cpython-311.pyc,,
nipype/utils/__pycache__/functions.cpython-311.pyc,,
nipype/utils/__pycache__/gpu_count.cpython-311.pyc,,
nipype/utils/__pycache__/imagemanip.cpython-311.pyc,,
nipype/utils/__pycache__/logger.cpython-311.pyc,,
nipype/utils/__pycache__/matlabtools.cpython-311.pyc,,
nipype/utils/__pycache__/misc.cpython-311.pyc,,
nipype/utils/__pycache__/nipype2boutiques.cpython-311.pyc,,
nipype/utils/__pycache__/nipype_cmd.cpython-311.pyc,,
nipype/utils/__pycache__/onetime.cpython-311.pyc,,
nipype/utils/__pycache__/profiler.cpython-311.pyc,,
nipype/utils/__pycache__/provenance.cpython-311.pyc,,
nipype/utils/__pycache__/spm_docs.cpython-311.pyc,,
nipype/utils/__pycache__/subprocess.cpython-311.pyc,,
nipype/utils/__pycache__/tmpdirs.cpython-311.pyc,,
nipype/utils/config.py,sha256=ADCjiGrxZdItJXnLYlnmwV3D7PKCc9uN5d5TAs5c1uk,12348
nipype/utils/datetime.py,sha256=eApKAIy6m2qHl4MGuU4O0mIRXxXAbC-ZiFrel0_YoT8,451
nipype/utils/docparse.py,sha256=CR8irunyRVsfcIuHmzMJF3rOpXEctAwD6SOilMi1K04,10825
nipype/utils/draw_gantt_chart.py,sha256=32DTrXJEWyVLCA9dcSsmxzoV7XumldYb7guMchyZ2WU,20664
nipype/utils/filemanip.py,sha256=IWAt3c97g8ubahT3xDNAUKESIVK6ls80NYCiAns2WUU,28739
nipype/utils/functions.py,sha256=KlzlXh5n1j8iqTUFRMBqVliL8650sv1PJPcJlz2m8M8,1519
nipype/utils/gpu_count.py,sha256=VgpLSmYsDZfusPvwbToJkh79OLfMJuhGUNmiHB7Sr2E,1887
nipype/utils/imagemanip.py,sha256=-7WXoEAdX7yU7rMOmuBv7bIfao4yBStHwWq3eM0khig,589
nipype/utils/logger.py,sha256=6mHnbwpw53d4IcnEO6Cu2liqhw2HidESsdmV0PFlmfU,4061
nipype/utils/matlabtools.py,sha256=m7TO1JViPUaj0vumoNVqMoRcGURKNe8tCd2tDrw76nw,2162
nipype/utils/misc.py,sha256=P8EvlUQd9sZ2PEc-eokBY13oDhI40dJFEFJ1XYzHZIA,10787
nipype/utils/nipype2boutiques.py,sha256=FTTrYQKJYR7U0ybbzegWS_cw0mmQjNsYxq0rEGfwTw0,22868
nipype/utils/nipype_cmd.py,sha256=31xtWZG8-BDWMaZsmDtrWN6RqnG9jiIbg4wjCie1-mU,2727
nipype/utils/onetime.py,sha256=eKPuCAjUyPu5I0GjyFqLlFQpwSugtL5lECsaIj-Qs2I,2571
nipype/utils/profiler.py,sha256=-ag3HpCdC-6Acno6PKaDgeHzZ8nttfv4csJUXn6JtjE,12046
nipype/utils/provenance.py,sha256=5apluWPa03HpwcwjCBldkLFgMZsK94dy6jOLM2GDxac,16021
nipype/utils/spm_docs.py,sha256=y7OUB5EmFrnBNDqOeJvnUGITtjh1FUv-E_ae4G5n_iA,1697
nipype/utils/subprocess.py,sha256=KfoPeV37vR5gIORu_K9dU9b_DQMJgNeBjpLxU23B804,5895
nipype/utils/tests/__init__.py,sha256=X-udbb5Bsd6NVPewbk99krbKk9br8TKe_L5QRj3N1Tk,1193
nipype/utils/tests/__pycache__/__init__.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_cmd.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_config.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_docparse.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_filemanip.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_functions.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_imagemanip.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_misc.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_nipype2boutiques.cpython-311.pyc,,
nipype/utils/tests/__pycache__/test_provenance.cpython-311.pyc,,
nipype/utils/tests/test_cmd.py,sha256=Gr_swj2WA5EJ1zqHTdv7cQ41DIqk3mios5I9KJL2m-w,2479
nipype/utils/tests/test_config.py,sha256=Zzb4ElqXCX4Qc8gkaq6sLJsf6umkY3yEAzX7YEnoUDw,11398
nipype/utils/tests/test_docparse.py,sha256=JMfAdvTB0-RlJy-Q84U_MjDPoVFvnQGM8uJ6zoQkclE,1394
nipype/utils/tests/test_filemanip.py,sha256=CbjQ8B50Zbaf482wIrcRUEUq7XeRkfPoSghn7FikWW8,26059
nipype/utils/tests/test_functions.py,sha256=U4CHuyfnyVleGO6KgvfiS3J36qZ1yHXBxnZMOVW90fs,983
nipype/utils/tests/test_imagemanip.py,sha256=lU39aGgDUodRT1UNiLjsl0w9bRCY-Uhtzjszp51fpqk,1248
nipype/utils/tests/test_misc.py,sha256=RlrWKXMqqZ4_rEtepZaJlWEe0ct4sf4iQvyGK4BhqiI,3694
nipype/utils/tests/test_nipype2boutiques.py,sha256=q-vohP0uvwDrUX80EdCX875eWKY13Dv0P0sqcDGB3n4,1583
nipype/utils/tests/test_provenance.py,sha256=tZTXl94yJkyPPG4NJfHdMMar8DnuTsVenUNsUg0FY8g,1347
nipype/utils/tmpdirs.py,sha256=HICKb1Nt7Uud3E7oBZPYVrNHFsqic99l2ko2objVgjk,1201
nipype/workflows/__init__.py,sha256=BMyNcNJvjl6Bicbfso0HrM4ntpTthfKFK3JzFJ2Baw4,1011
nipype/workflows/__pycache__/__init__.cpython-311.pyc,,
