Metadata-Version: 2.1
Name: prov
Version: 2.0.1
Summary: A library for W3C Provenance Data Model supporting PROV-JSON, PROV-XML and PROV-O (RDF)
Home-page: https://github.com/trungdong/prov
Author: T<PERSON>h
Author-email: <EMAIL>
License: MIT
Project-URL: Bug Reports, https://github.com/trungdong/prov/issues
Project-URL: Source, https://github.com/trungdong/prov
Keywords: provenance,graph,model,PROV,PROV-DM,PROV-JSON,W3C,PROV-XML,PROV-N,PROV-O,RDF
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Scientific/Engineering :: Information Analysis
Classifier: Topic :: Security
Classifier: Topic :: System :: Logging
Requires-Python: >=3.6, <4
License-File: LICENSE
License-File: AUTHORS.rst
Requires-Dist: python-dateutil >=2.2
Requires-Dist: networkx >=2.0
Requires-Dist: lxml >=3.3.5
Requires-Dist: rdflib <7,>=4.2.1
Provides-Extra: dot
Requires-Dist: pydot >=1.2.0 ; extra == 'dot'

============
Introduction
============


.. image:: https://badge.fury.io/py/prov.svg
  :target: http://badge.fury.io/py/prov
  :alt: Latest Release
.. image:: https://github.com/trungdong/prov/workflows/CI/badge.svg?branch=master
  :target: https://github.com/trungdong/prov/actions?workflow=CI
  :alt: CI Status
.. image:: https://img.shields.io/coveralls/trungdong/prov.svg
  :target: https://coveralls.io/r/trungdong/prov?branch=master
  :alt: Coverage Status
.. image:: https://img.shields.io/pypi/wheel/prov.svg
  :target: https://pypi.python.org/pypi/prov/
  :alt: Wheel Status
.. image:: https://img.shields.io/pypi/pyversions/prov.svg
  :target: https://pypi.python.org/pypi/prov/
  :alt: Supported Python version
.. image:: https://img.shields.io/pypi/l/prov.svg
  :target: https://pypi.python.org/pypi/prov/
  :alt: License


A library for W3C Provenance Data Model supporting PROV-O (RDF), PROV-XML, PROV-JSON import/export

* Free software: MIT license
* Documentation: http://prov.readthedocs.io/.
* Python 3 only.

Features
--------

* An implementation of the `W3C PROV Data Model <http://www.w3.org/TR/prov-dm/>`_ in Python.
* In-memory classes for PROV assertions, which can then be output as `PROV-N <http://www.w3.org/TR/prov-n/>`_
* Serialization and deserialization support: `PROV-O <http://www.w3.org/TR/prov-o/>`_ (RDF), `PROV-XML <http://www.w3.org/TR/prov-xml/>`_ and `PROV-JSON <http://www.w3.org/Submission/prov-json/>`_.
* Exporting PROV documents into various graphical formats (e.g. PDF, PNG, SVG).
* Convert a PROV document to a `Networkx MultiDiGraph <https://networkx.github.io/documentation/stable/reference/classes/multidigraph.html>`_ and back.


Uses
^^^^

See `a short tutorial  <http://trungdong.github.io/prov-python-short-tutorial.html>`_ for using this package.

This package is used extensively by `ProvStore <https://openprovenance.org/store/>`_,
a free online repository for provenance documents.
