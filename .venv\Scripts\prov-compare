#!D:\ONLSOL_PROJ\.venv\Scripts\python.exe
# encoding: utf-8
"""
prov-compare -- Compare two PROV-JSON, PROV-XML, or RDF (PROV-O) files for equivalence

@author:     <PERSON><PERSON>

@copyright:  2016 University of Southampton, United Kingdom. All rights reserved.

@license:    MIT Licence

@contact:    <EMAIL>
@deffield    updated: 2016-10-19
"""

from argparse import ArgumentParser, RawDescriptionHelpFormatter, FileType
import os
import sys
import logging
import traceback

from prov.model import ProvDocument


logger = logging.getLogger(__name__)

__all__ = []
__version__ = 0.1
__date__ = '2015-06-16'
__updated__ = '2016-10-19'

DEBUG = 0
TESTRUN = 0
PROFILE = 0


class CLIError(Exception):
    """Generic exception to raise and log different fatal errors."""
    def __init__(self, msg):
        super(CLIError).__init__(type(self))
        self.msg = "E: %s" % msg

    def __str__(self):
        return self.msg


def main(argv=None):  # IGNORE:C0111
    """Command line options."""

    if argv is None:
        argv = sys.argv
    else:
        sys.argv.extend(argv)

    program_name = os.path.basename(sys.argv[0])
    program_version = "v%s" % __version__
    program_build_date = str(__updated__)
    program_version_message = '%%(prog)s %s (%s)' % (program_version, program_build_date)
    program_shortdesc = __import__('__main__').__doc__.split("\n")[1]
    program_license = '''%s

  Created by Trung Dong Huynh on %s.
  Copyright 2016 University of Southampton. All rights reserved.

  Licensed under the MIT License
  https://github.com/trungdong/prov/blob/master/LICENSE

  Distributed on an "AS IS" basis without warranties
  or conditions of any kind, either express or implied.

USAGE
''' % (program_shortdesc, str(__date__))

    try:
        # Setup argument parser
        parser = ArgumentParser(description=program_license, formatter_class=RawDescriptionHelpFormatter)
        parser.add_argument('file1', nargs='?', type=FileType('r'))
        parser.add_argument('file2', nargs='?', type=FileType('r'))
        parser.add_argument('-f', '--format1', dest='format1', action='store', default='json',
                            help='File 1\'s format: json or xml')
        parser.add_argument('-F', '--format2', dest='format2', action='store', default='json',
                            help='File 2\'s format: json or xml')
        parser.add_argument('-V', '--version', action='version', version=program_version_message)

        args = None
        try:
            # Process arguments
            args = parser.parse_args()
            doc1 = ProvDocument.deserialize(args.file1, format=args.format1.lower())
            doc2 = ProvDocument.deserialize(args.file2, format=args.format2.lower())
            return doc1 != doc2

        finally:
            if args:
                if args.file1:
                    args.file1.close()
                if args.file2:
                    args.file2.close()

    except Exception as e:
        if DEBUG or TESTRUN:
            traceback.print_exc()
            raise e
        indent = len(program_name) * " "
        sys.stderr.write(program_name + ": " + str(e) + "\n")
        sys.stderr.write(indent + "  for help use --help")
        return 2


if __name__ == "__main__":
    logging.basicConfig(level=(logging.DEBUG if DEBUG else logging.INFO))
    if TESTRUN:
        import doctest
        doctest.testmod()
    if PROFILE:
        import cProfile
        import pstats
        profile_filename = 'prov_compare_profile.txt'
        cProfile.run('main()', profile_filename)
        statsfile = open("profile_stats.txt", "wb")
        p = pstats.Stats(profile_filename, stream=statsfile)
        stats = p.strip_dirs().sort_stats('cumulative')
        stats.print_stats()
        statsfile.close()
        sys.exit(0)
    sys.exit(main())
